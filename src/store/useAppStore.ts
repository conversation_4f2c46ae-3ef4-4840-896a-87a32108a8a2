import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { TraceData } from '@/types/trace';
import { AnalysisSession, AnalysisStep } from '@/types/analysis';
import { generateMockTraceData } from '@/data/mockTraceData';

export type AppView = 'input' | 'analysis' | 'trace-info' | 'badcase-analysis' | 'ai-optimization';

interface AppState {
  // UI状态
  currentView: AppView;
  isLoading: boolean;
  
  // 数据状态
  currentTraceId: string;
  trace: TraceData | null;
  analysisSession: AnalysisSession | null;
  
  // Actions
  setCurrentView: (view: AppView) => void;
  setIsLoading: (loading: boolean) => void;
  setCurrentTraceId: (traceId: string) => void;
  setTrace: (trace: TraceData | null) => void;
  setAnalysisSession: (session: AnalysisSession | null) => void;
  
  // 业务逻辑
  startTraceAnalysis: (traceId: string) => Promise<void>;
  handleStepClick: (stepId: string, isCompleted: boolean) => void;
  handleBackToInput: () => void;
  handleBackToAnalysis: () => void;
  updateAnalysisStep: (stepIndex: number, status: AnalysisStep['status'], completedAt?: string) => void;
  processNextStep: () => Promise<void>;
}

export const useAppStore = create<AppState>()(
  devtools(
    persist(
      (set, get) => ({
        // 初始状态
        currentView: 'input',
        isLoading: false,
        currentTraceId: '',
        trace: null,
        analysisSession: null,

        // 基础设置器
        setCurrentView: (view) => set({ currentView: view }),
        setIsLoading: (loading) => set({ isLoading: loading }),
        setCurrentTraceId: (traceId) => set({ currentTraceId: traceId }),
        setTrace: (trace) => set({ trace }),
        setAnalysisSession: (session) => set({ analysisSession: session }),

        // 业务逻辑
        startTraceAnalysis: async (traceId: string) => {
          set({ isLoading: true, currentTraceId: traceId });
          
          try {
            // 创建新的分析会话
            const newSession: AnalysisSession = {
              traceId,
              startedAt: new Date().toISOString(),
              currentStep: 0,
              steps: [
                {
                  id: 'trace-info',
                  name: '链路信息获取',
                  description: '获取完整的调用链路和性能数据',
                  status: 'pending'
                },
                {
                  id: 'badcase-analysis',
                  name: 'BadCase分析',
                  description: '识别性能瓶颈和异常节点',
                  status: 'pending'
                },
                {
                  id: 'ai-optimization',
                  name: 'AI调优',
                  description: '生成最佳输入输出建议',
                  status: 'pending'
                }
              ]
            };

            set({ 
              analysisSession: newSession, 
              currentView: 'analysis',
              isLoading: false 
            });

            // 自动开始处理第一步
            setTimeout(() => {
              get().processNextStep();
            }, 1000);

          } catch (error) {
            set({ isLoading: false });
            throw error;
          }
        },

        handleStepClick: (stepId: string, isCompleted: boolean) => {
          if (!isCompleted) return;
          
          const { currentTraceId } = get();
          // 生成对应的trace数据
          const traceData = generateMockTraceData(currentTraceId);
          set({ 
            trace: traceData,
            currentView: stepId as AppView
          });
        },

        handleBackToInput: () => {
          set({
            trace: null,
            currentView: 'input',
            currentTraceId: '',
            analysisSession: null
          });
        },

        handleBackToAnalysis: () => {
          set({ currentView: 'analysis' });
        },

        updateAnalysisStep: (stepIndex: number, status: AnalysisStep['status'], completedAt?: string) => {
          const { analysisSession } = get();
          if (!analysisSession) return;

          const updatedSession = {
            ...analysisSession,
            steps: analysisSession.steps.map((step, index) => 
              index === stepIndex 
                ? { ...step, status, ...(completedAt && { completedAt }) }
                : step
            ),
            currentStep: status === 'completed' ? stepIndex + 1 : analysisSession.currentStep
          };

          set({ analysisSession: updatedSession });
        },

        processNextStep: async () => {
          const { analysisSession } = get();
          if (!analysisSession) return;

          const currentStepIndex = analysisSession.currentStep;
          const currentStep = analysisSession.steps[currentStepIndex];
          
          if (!currentStep || currentStep.status === 'completed') return;

          // 更新步骤为loading状态
          get().updateAnalysisStep(currentStepIndex, 'loading');

          // 模拟异步处理
          const delay = currentStep.id === 'trace-info' ? 2000 : 
                       currentStep.id === 'badcase-analysis' ? 3000 : 4000;
          
          await new Promise(resolve => setTimeout(resolve, delay));

          // 标记步骤完成
          const completedAt = new Date().toISOString();
          get().updateAnalysisStep(currentStepIndex, 'completed', completedAt);

          // 检查是否还有下一步
          const { analysisSession: updatedSession } = get();
          if (updatedSession && updatedSession.currentStep < updatedSession.steps.length) {
            // 继续处理下一步
            setTimeout(() => {
              get().processNextStep();
            }, 500);
          }
        }
      }),
      {
        name: 'app-store',
        partialize: (state) => ({
          analysisSession: state.analysisSession,
          currentTraceId: state.currentTraceId,
          currentView: state.currentView,
        }),
      }
    ),
    { name: 'AppStore' }
  )
);