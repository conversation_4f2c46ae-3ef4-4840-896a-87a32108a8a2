import { TraceData, TraceNode } from '@/types/trace';

export const generateMockTraceData = (traceId: string): TraceData => {
  const createNode = (
    id: string, 
    name: string, 
    type: TraceNode['type'], 
    nodeType: TraceNode['nodeType'],
    level: number,
    parent?: string
  ): TraceNode => ({
    id,
    name,
    type,
    nodeType,
    status: Math.random() > 0.1 ? 'success' : 'error',
    startTime: new Date(Date.now() - Math.random() * 60000).toISOString(),
    endTime: new Date(Date.now() - Math.random() * 30000).toISOString(),
    duration: Math.floor(Math.random() * 5000),
    level,
    parent,
    children: [],
    input: nodeType === 'llm' ? {
      messages: [
        { role: 'user', content: 'Analyze the following data and provide insights...' },
        { role: 'system', content: 'You are a helpful data analyst.' }
      ]
    } : nodeType === 'tool' ? {
      query: 'SELECT * FROM users WHERE active = true',
      parameters: { limit: 100 }
    } : {
      context: 'Processing user request...'
    },
    output: nodeType === 'llm' ? {
      content: 'Based on the analysis, I found several key insights...',
      reasoning: 'The data shows a clear trend in user engagement.'
    } : nodeType === 'tool' ? {
      results: [
        { id: 1, name: '<PERSON> Doe', email: '<EMAIL>' },
        { id: 2, name: 'Jane Smith', email: '<EMAIL>' }
      ],
      count: 2
    } : {
      result: 'Task completed successfully'
    },
    tokens: nodeType === 'llm' ? {
      prompt: Math.floor(Math.random() * 1000) + 100,
      completion: Math.floor(Math.random() * 800) + 50,
      total: 0
    } : undefined,
    model: nodeType === 'llm' ? ['gpt-4', 'gpt-3.5-turbo', 'claude-3'][Math.floor(Math.random() * 3)] : undefined,
    metadata: {
      temperature: nodeType === 'llm' ? 0.7 : undefined,
      maxTokens: nodeType === 'llm' ? 2048 : undefined,
      provider: nodeType === 'llm' ? 'openai' : undefined,
      tool: nodeType === 'tool' ? 'database' : undefined,
      version: '1.0.0'
    }
  });

  // Create a complex trace structure
  const root = createNode(
    'root',
    'User Query Analysis',
    'trace',
    'agent',
    0
  );

  // Level 1 - Main agent steps
  const planning = createNode(
    'planning',
    'Query Planning',
    'span',
    'agent',
    1,
    'root'
  );

  const dataRetrieval = createNode(
    'data-retrieval',
    'Data Retrieval',
    'span',
    'span',
    1,
    'root'
  );

  const analysis = createNode(
    'analysis',
    'Data Analysis',
    'span',
    'agent',
    1,
    'root'
  );

  // Level 2 - Sub-operations
  const llmPlanning = createNode(
    'llm-planning',
    'LLM Query Understanding',
    'generation',
    'llm',
    2,
    'planning'
  );

  const dbQuery = createNode(
    'db-query',
    'Database Query',
    'event',
    'tool',
    2,
    'data-retrieval'
  );

  const cacheCheck = createNode(
    'cache-check',
    'Cache Lookup',
    'event',
    'tool',
    2,
    'data-retrieval'
  );

  const llmAnalysis = createNode(
    'llm-analysis',
    'LLM Data Analysis',
    'generation',
    'llm',
    2,
    'analysis'
  );

  const resultFormatting = createNode(
    'result-formatting',
    'Result Formatting',
    'span',
    'span',
    2,
    'analysis'
  );

  // Level 3 - Detailed operations
  const vectorSearch = createNode(
    'vector-search',
    'Vector Similarity Search',
    'event',
    'tool',
    3,
    'llm-planning'
  );

  const contextRetrieval = createNode(
    'context-retrieval',
    'Context Retrieval',
    'event',
    'tool',
    3,
    'llm-planning'
  );

  const finalGeneration = createNode(
    'final-generation',
    'Final Response Generation',
    'generation',
    'llm',
    3,
    'result-formatting'
  );

  // Build the tree structure
  planning.children = [llmPlanning];
  dataRetrieval.children = [dbQuery, cacheCheck];
  analysis.children = [llmAnalysis, resultFormatting];

  llmPlanning.children = [vectorSearch, contextRetrieval];
  resultFormatting.children = [finalGeneration];

  root.children = [planning, dataRetrieval, analysis];

  // Calculate total tokens for LLM nodes
  const calculateTokens = (node: TraceNode): void => {
    if (node.tokens) {
      node.tokens.total = node.tokens.prompt + node.tokens.completion;
    }
    node.children.forEach(calculateTokens);
  };

  calculateTokens(root);

  // Calculate total tokens and cost for the trace
  const getAllNodes = (node: TraceNode): TraceNode[] => {
    let nodes = [node];
    node.children.forEach(child => {
      nodes = nodes.concat(getAllNodes(child));
    });
    return nodes;
  };

  const allNodes = getAllNodes(root);
  const totalTokens = allNodes.reduce((sum, node) => 
    sum + (node.tokens?.total || 0), 0
  );

  return {
    id: traceId,
    name: 'User Query Analysis Pipeline',
    userId: 'user-123',
    sessionId: 'session-456',
    timestamp: new Date(Date.now() - Math.random() * 3600000).toISOString(),
    duration: Math.floor(Math.random() * 15000) + 5000,
    status: allNodes.some(node => node.status === 'error') ? 'error' : 'success',
    input: {
      query: 'What are the engagement patterns of our top users?',
      filters: {
        timeRange: '30d',
        userSegment: 'premium'
      }
    },
    output: {
      summary: 'Premium users show 40% higher engagement during weekdays.',
      insights: [
        'Peak activity occurs between 9-11 AM',
        'Mobile engagement is 60% higher than desktop',
        'Feature adoption rate is 85% for premium users'
      ],
      recommendations: [
        'Optimize mobile experience for better engagement',
        'Schedule important notifications for peak hours'
      ]
    },
    rootNode: root,
    totalTokens,
    totalCost: totalTokens * 0.00002, // Example cost calculation
    metadata: {
      environment: 'production',
      version: '2.1.0',
      region: 'us-east-1'
    },
    tags: ['analytics', 'user-engagement', 'premium']
  };
};

export const SAMPLE_TRACE_IDS = [
  'trace_12345678901234567890',
  'trace_98765432109876543210',
  'trace_11111111111111111111',
  'trace_22222222222222222222',
  'trace_33333333333333333333'
];