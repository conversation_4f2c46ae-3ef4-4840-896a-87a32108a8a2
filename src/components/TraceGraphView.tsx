import { useMemo } from 'react';
import { <PERSON><PERSON>, Clock, Alert<PERSON><PERSON>gle, CheckCircle, XCircle, ArrowDown } from 'lucide-react';
import { TraceNode } from '@/types/trace';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface TraceGraphViewProps {
  rootNode: TraceNode;
  selectedNode: TraceNode | null;
  onNodeSelect: (node: TraceNode) => void;
  searchQuery?: string;
}

interface GraphNode extends TraceNode {
  x: number;
  y: number;
  width: number;
  height: number;
}

const NODE_WIDTH = 200;
const NODE_HEIGHT = 80;
const LEVEL_HEIGHT = 120;
const NODE_SPACING = 220;

export const TraceGraphView = ({ rootNode, selectedNode, onNodeSelect, searchQuery = '' }: TraceGraphViewProps) => {
  const graphData = useMemo(() => {
    const allNodes: TraceNode[] = [];
    
    // Collect all nodes with their levels
    const collectNodes = (node: TraceNode, level: number) => {
      // Filter nodes based on search query
      const shouldInclude = !searchQuery || 
        node.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        node.nodeType.toLowerCase().includes(searchQuery.toLowerCase());
      
      if (shouldInclude) {
        allNodes.push({ ...node, level });
      }
      node.children.forEach(child => collectNodes(child, level + 1));
    };
    
    collectNodes(rootNode, 0);

    // Group nodes by level
    const nodesByLevel = allNodes.reduce((acc, node) => {
      if (!acc[node.level]) acc[node.level] = [];
      acc[node.level].push(node);
      return acc;
    }, {} as Record<number, TraceNode[]>);

    // Calculate positions
    const graphNodes: GraphNode[] = [];
    
    Object.entries(nodesByLevel).forEach(([level, nodes]) => {
      const levelNum = parseInt(level);
      const totalWidth = (nodes.length - 1) * NODE_SPACING;
      const startX = -totalWidth / 2;
      
      nodes.forEach((node, index) => {
        graphNodes.push({
          ...node,
          x: startX + index * NODE_SPACING,
          y: levelNum * LEVEL_HEIGHT,
          width: NODE_WIDTH,
          height: NODE_HEIGHT
        });
      });
    });

    return { nodes: graphNodes, levels: Object.keys(nodesByLevel).length };
  }, [rootNode, searchQuery]);

  const getNodeIcon = (nodeType: string, status: string) => {
    if (status === 'error') return <XCircle className="h-5 w-5 text-trace-error" />;
    if (status === 'pending') return <AlertTriangle className="h-5 w-5 text-trace-warning" />;
    
    switch (nodeType) {
      case 'llm': return <Zap className="h-5 w-5 text-node-llm" />;
      case 'agent': return <CheckCircle className="h-5 w-5 text-node-agent" />;
      case 'tool': return <Clock className="h-5 w-5 text-node-tool" />;
      case 'span': return <Clock className="h-5 w-5 text-node-span" />;
      case 'generation': return <Zap className="h-5 w-5 text-node-generation" />;
      default: return <CheckCircle className="h-5 w-5 text-trace-success" />;
    }
  };

  const getNodeTypeColor = (nodeType: string) => {
    switch (nodeType) {
      case 'llm': return 'border-node-llm bg-node-llm/5 hover:bg-node-llm/10';
      case 'agent': return 'border-node-agent bg-node-agent/5 hover:bg-node-agent/10';
      case 'tool': return 'border-node-tool bg-node-tool/5 hover:bg-node-tool/10';
      case 'span': return 'border-node-span bg-node-span/5 hover:bg-node-span/10';
      case 'generation': return 'border-node-generation bg-node-generation/5 hover:bg-node-generation/10';
      default: return 'border-border bg-card hover:bg-muted/50';
    }
  };

  const formatDuration = (ms?: number) => {
    if (!ms) return '-';
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const containerWidth = Math.max(800, NODE_SPACING * Math.max(...Object.values(
    graphData.nodes.reduce((acc, node) => {
      acc[node.level] = (acc[node.level] || 0) + 1;
      return acc;
    }, {} as Record<number, number>)
  )));

  const containerHeight = graphData.levels * LEVEL_HEIGHT + 100;

  return (
    <div className="relative overflow-auto" style={{ height: '600px' }}>
      {searchQuery && (
        <div className="text-sm text-muted-foreground mb-4 text-center">
          搜索: "{searchQuery}" - 找到 {graphData.nodes.length} 个节点
        </div>
      )}
      <div 
        className="relative"
        style={{ 
          width: containerWidth + 'px', 
          height: containerHeight + 'px',
          margin: '0 auto'
        }}
      >
        {/* Render connections */}
        <svg 
          className="absolute inset-0 pointer-events-none"
          width={containerWidth}
          height={containerHeight}
        >
          {graphData.nodes.map(node => 
            node.children.map(child => {
              const childNode = graphData.nodes.find(n => n.id === child.id);
              if (!childNode) return null;
              
              const fromX = node.x + NODE_WIDTH / 2 + containerWidth / 2;
              const fromY = node.y + NODE_HEIGHT;
              const toX = childNode.x + NODE_WIDTH / 2 + containerWidth / 2;
              const toY = childNode.y;
              
              return (
                <g key={`${node.id}-${child.id}`}>
                  <line
                    x1={fromX}
                    y1={fromY}
                    x2={toX}
                    y2={toY}
                    stroke="hsl(var(--border))"
                    strokeWidth={2}
                    markerEnd="url(#arrowhead)"
                  />
                </g>
              );
            })
          )}
          
          {/* Arrow marker */}
          <defs>
            <marker
              id="arrowhead"
              markerWidth="10"
              markerHeight="7"
              refX="9"
              refY="3.5"
              orient="auto"
            >
              <polygon
                points="0 0, 10 3.5, 0 7"
                fill="hsl(var(--border))"
              />
            </marker>
          </defs>
        </svg>

        {/* Render nodes */}
        {graphData.nodes.map(node => (
          <Card
            key={node.id}
            className={cn(
              "absolute p-3 cursor-pointer transition-all duration-200 border-2",
              getNodeTypeColor(node.nodeType),
              selectedNode?.id === node.id && "ring-2 ring-trace-accent ring-offset-2 border-trace-accent"
            )}
            style={{
              left: node.x + containerWidth / 2 - NODE_WIDTH / 2,
              top: node.y,
              width: NODE_WIDTH,
              height: NODE_HEIGHT
            }}
            onClick={() => onNodeSelect(node)}
          >
            <div className="flex items-start gap-2 h-full">
              {getNodeIcon(node.nodeType, node.status)}
              
              <div className="flex-1 min-w-0 space-y-1">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-sm truncate">{node.name}</h4>
                  <Badge variant="secondary" className="text-xs">
                    {node.nodeType}
                  </Badge>
                </div>
                
                <div className="text-xs text-muted-foreground space-y-1">
                  <div className="flex justify-between">
                    <span>时长:</span>
                    <span>{formatDuration(node.duration)}</span>
                  </div>
                  
                  {node.tokens && (
                    <div className="flex justify-between">
                      <span>Tokens:</span>
                      <span>{node.tokens.total}</span>
                    </div>
                  )}
                  
                  {node.model && (
                    <div className="text-xs font-mono truncate">
                      {node.model}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};