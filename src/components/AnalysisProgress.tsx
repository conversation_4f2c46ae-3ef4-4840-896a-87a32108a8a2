import { useEffect, useState } from 'react';
import { Check, Loader2, Play, ArrowLeft } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { AnalysisStep } from '@/types/analysis';
import { useAppStore } from '@/store/useAppStore';
import { cn } from '@/lib/utils';

export const AnalysisProgress = () => {
  const { 
    analysisSession, 
    currentTraceId,
    handleStepClick,
    handleBackToInput
  } = useAppStore();
  
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    if (!analysisSession) return;
    const completedSteps = analysisSession.steps.filter(s => s.status === 'completed').length;
    setProgress((completedSteps / analysisSession.steps.length) * 100);
  }, [analysisSession]);

  const handleStepClickLocal = (step: AnalysisStep, index: number) => {
    // 只有完成的步骤才可以点击
    if (step.status === 'completed') {
      handleStepClick(step.id, true);
    }
  };

  const getStepIcon = (step: AnalysisStep, index: number) => {
    if (step.status === 'completed') {
      return <Check className="h-5 w-5 text-green-500" />;
    } else if (step.status === 'loading') {
      return <Loader2 className="h-5 w-5 animate-spin text-blue-500" />;
    } else {
      return (
        <div className={cn(
          "h-5 w-5 rounded-full border-2 flex items-center justify-center text-xs font-medium",
          step.status === 'pending' ? "border-muted text-muted-foreground" : ""
        )}>
          {index + 1}
        </div>
      );
    }
  };

  if (!analysisSession) {
    return <div>Loading...</div>;
  }

  return (
    <div className="w-full max-w-4xl mx-auto p-6 space-y-8">
      {/* 返回首页按钮 */}
      <div className="flex items-center">
        <Button variant="ghost" size="sm" onClick={handleBackToInput} className="flex items-center gap-2">
          <ArrowLeft className="h-4 w-4" />
          返回首页
        </Button>
      </div>
      <div className="text-center space-y-6">
        <div className="space-y-2">
          <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            {progress === 100 ? '分析已完成' : '分析进行中...'}
          </h2>
          <p className="text-muted-foreground">
            {progress === 100 ? '已完成分析' : '正在分析'} Trace: <span className="font-mono text-sm bg-muted px-2 py-1 rounded">{currentTraceId}</span>
          </p>
        </div>
        
        <div className="space-y-4">
          <div className="relative">
            <Progress value={progress} className="w-full h-3" />
            <div className="absolute inset-0 flex items-center justify-center text-xs font-medium text-white mix-blend-difference">
              {Math.round(progress)}%
            </div>
          </div>
          <p className="text-sm text-muted-foreground">
            完成进度: {analysisSession.steps.filter(s => s.status === 'completed').length} / {analysisSession.steps.length} 步骤
          </p>
        </div>
      </div>

      <div className="space-y-6">
        {analysisSession.steps.map((step, index) => (
          <Card 
            key={step.id} 
            className={cn(
              "p-6 transition-all duration-300 hover:shadow-lg",
              step.status === 'completed' ? "cursor-pointer border-green-200 bg-gradient-to-r from-green-50 to-emerald-50 hover:from-green-100 hover:to-emerald-100" : "",
              step.status === 'loading' ? "border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 animate-pulse" : "",
              step.status === 'pending' ? "border-gray-200 bg-gray-50/50" : ""
            )}
            onClick={() => handleStepClickLocal(step, index)}
          >
            <div className="flex items-center gap-6">
              <div className="flex-shrink-0">
                <div className={cn(
                  "w-12 h-12 rounded-full flex items-center justify-center",
                  step.status === 'completed' ? "bg-green-100" : 
                  step.status === 'loading' ? "bg-blue-100" : "bg-gray-100"
                )}>
                  {getStepIcon(step, index)}
                </div>
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold mb-1">{step.name}</h3>
                    <p className="text-sm text-muted-foreground">
                      {step.description}
                    </p>
                  </div>
                  
                  {step.status === 'completed' && (
                    <Button size="sm" variant="outline" className="ml-4 shadow-sm">
                      <Play className="h-3 w-3 mr-2" />
                      查看详情
                    </Button>
                  )}
                  {step.status === 'loading' && (
                    <Button size="sm" variant="outline" className="ml-4 shadow-sm" disabled>
                      <Loader2 className="h-3 w-3 mr-2 animate-spin" />
                      分析进行中...
                    </Button>
                  )}
                </div>
                
                <div className="mt-3">
                  {step.status === 'loading' && (
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                      <p className="text-sm text-blue-600 font-medium">
                        正在处理中...
                      </p>
                    </div>
                  )}
                  {step.status === 'completed' && step.completedAt && (
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <p className="text-sm text-green-600 font-medium">
                        已完成 - {new Date(step.completedAt).toLocaleTimeString()}
                      </p>
                    </div>
                  )}
                  {step.status === 'pending' && (
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                      <p className="text-sm text-gray-500">
                        等待处理...
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};