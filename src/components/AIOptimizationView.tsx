import { useState } from 'react';
import { ArrowLeft, Search, Filter, Sparkles, TrendingUp, Target, Play, RotateCcw, Loader2, CheckCircle, Zap, Download, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { TraceData } from '@/types/trace';
import { OptimizedNode } from '@/types/analysis';
import { TraceGraphView } from './TraceGraphView';
import { NodeDetailPanel } from './NodeDetailPanel';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

interface AIOptimizationViewProps {
  trace: TraceData;
  onBack: () => void;
  analysisSession?: any;
}

export const AIOptimizationView = ({ trace, onBack, analysisSession }: AIOptimizationViewProps) => {
  const [selectedNode, setSelectedNode] = useState<OptimizedNode | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [customInput, setCustomInput] = useState('');
  const [isTestingOutput, setIsTestingOutput] = useState(false);
  const [testOutput, setTestOutput] = useState<any>(null);
  const { toast } = useToast();

  // 模拟添加优化信息到节点
  const addOptimizationInfo = (node: any): OptimizedNode => {
    const optimizations = [
      {
        type: 'prompt' as const,
        description: '优化prompt结构，提高响应质量',
        performance: {
          before: 2.3,
          after: 1.8,
          improvement: '22%性能提升'
        }
      },
      {
        type: 'parameter' as const,
        description: '调整temperature和max_tokens参数',
        performance: {
          before: 1.9,
          after: 1.2,
          improvement: '37%响应速度提升'
        }
      },
      {
        type: 'model' as const,
        description: '推荐使用更适合的模型',
        performance: {
          before: 3.1,
          after: 1.5,
          improvement: '52%成本降低'
        }
      }
    ];

    const shouldOptimize = Math.random() > 0.4; // 60%的节点有优化建议
    const optimization = optimizations[Math.floor(Math.random() * optimizations.length)];
    
    return {
      ...node,
      isOptimal: !shouldOptimize,
      optimization: shouldOptimize ? optimization : undefined,
      optimizedInput: shouldOptimize ? {
        ...node.input,
        optimized: true,
        improvements: ['更精确的指令', '减少冗余内容', '优化参数设置']
      } : node.input,
      optimizedOutput: shouldOptimize ? {
        ...node.output,
        quality_score: 0.95,
        confidence: 0.92
      } : node.output,
      children: node.children?.map(addOptimizationInfo) || []
    };
  };

  const enhancedRootNode = addOptimizationInfo(trace.rootNode);

  const getOptimizationStats = (node: OptimizedNode): { total: number; optimized: number; optimal: number } => {
    let stats = { total: 1, optimized: 0, optimal: 0 };
    
    if (node.isOptimal) {
      stats.optimal++;
    } else if (node.optimization) {
      stats.optimized++;
    }
    
    node.children?.forEach(child => {
      const childStats = getOptimizationStats(child);
      stats.total += childStats.total;
      stats.optimized += childStats.optimized;
      stats.optimal += childStats.optimal;
    });
    
    return stats;
  };

  const optimizationStats = getOptimizationStats(enhancedRootNode);

  const handleTestOutput = async () => {
    if (!selectedNode || !customInput.trim()) {
      toast({
        title: "请输入测试内容",
        description: "请先输入要测试的input内容",
        variant: "destructive"
      });
      return;
    }

    setIsTestingOutput(true);
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const mockOutput = {
        result: "这是基于优化input生成的测试输出",
        timestamp: new Date().toISOString(),
        confidence: 0.94,
        processing_time: 1.2,
        improved_metrics: {
          accuracy: "+15%",
          speed: "+25%",
          cost: "-30%"
        }
      };
      
      setTestOutput(mockOutput);
      
      toast({
        title: "测试完成",
        description: "成功生成优化后的输出结果"
      });
    } catch (error) {
      toast({
        title: "测试失败",
        description: "API调用出现错误，请稍后重试",
        variant: "destructive"
      });
    } finally {
      setIsTestingOutput(false);
    }
  };

  const getOptimizationTypeColor = (type: string) => {
    switch (type) {
      case 'prompt': return 'text-blue-600 bg-blue-100';
      case 'parameter': return 'text-green-600 bg-green-100';
      case 'model': return 'text-purple-600 bg-purple-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getOptimizationIcon = (type: string) => {
    switch (type) {
      case 'prompt': return '📝';
      case 'parameter': return '⚙️';
      case 'model': return '🤖';
      default: return '✨';
    }
  };

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* 头部工具栏 */}
      <div className="flex-none border-b bg-card/50">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={onBack} className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              返回分析流程
            </Button>
            
            <div className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-yellow-500" />
              <h1 className="text-lg font-semibold">AI调优中心</h1>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <div className="flex gap-2">
              <Badge variant="outline" className="text-green-600">{optimizationStats.optimal} 最优</Badge>
              <Badge variant="outline" className="text-blue-600">{optimizationStats.optimized} 可优化</Badge>
              <Badge variant="outline">{optimizationStats.total - optimizationStats.optimal - optimizationStats.optimized} 待分析</Badge>
            </div>
          </div>
        </div>

        {/* 搜索栏 */}
        <div className="px-4 pb-4">
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索节点..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        
        {/* 后台分析进度提示 */}
        {analysisSession && (
          <div className="border-t bg-blue-50/50 p-3">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                {analysisSession.steps.every((step: any) => step.status === 'completed') ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                )}
                <span className="text-sm font-medium text-blue-700">
                  {analysisSession.steps.every((step: any) => step.status === 'completed') ? '分析已完成' : '后台分析进行中'}
                </span>
              </div>
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                {analysisSession.steps?.map((step: any, index: number) => (
                  <div key={step.id} className="flex items-center gap-1">
                    {step.status === 'completed' ? (
                      <CheckCircle className="h-3 w-3 text-green-500" />
                    ) : step.status === 'loading' ? (
                      <Loader2 className="h-3 w-3 animate-spin text-blue-500" />
                    ) : (
                      <div className="h-3 w-3 rounded-full border border-gray-300" />
                    )}
                    <span className={step.status === 'completed' ? 'text-green-600' : step.status === 'loading' ? 'text-blue-600' : 'text-gray-500'}>
                      {step.name}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 主内容区域 */}
      <div className="flex-1 flex min-h-0">
        {/* 图形视图 */}
        <div className="flex-1 p-4">
          <TraceGraphView
            rootNode={enhancedRootNode as any}
            selectedNode={selectedNode as any}
            onNodeSelect={(node) => setSelectedNode(node as OptimizedNode)}
            searchQuery={searchQuery}
          />
        </div>

        {/* 详情面板 */}
        <div className="w-[500px] border-l bg-card/20 flex flex-col">
          <div className="p-4 border-b">
            <h3 className="font-medium">
              {selectedNode ? '节点优化详情' : '优化概览'}
            </h3>
          </div>
          
          <div className="flex-1 overflow-auto p-4">
            {selectedNode ? (
              <Tabs defaultValue="optimization" className="space-y-4">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="optimization">优化信息</TabsTrigger>
                  <TabsTrigger value="testing">调试测试</TabsTrigger>
                </TabsList>
                
                <TabsContent value="optimization" className="space-y-4">
                  <NodeDetailPanel node={selectedNode as any} />
                  
                  {selectedNode.optimization && (
                    <Card className="p-4 space-y-3">
                      <div className="flex items-center gap-2">
                        <span className="text-xl">{getOptimizationIcon(selectedNode.optimization.type)}</span>
                        <h4 className="font-semibold">优化建议</h4>
                        <Badge className={cn("text-xs", getOptimizationTypeColor(selectedNode.optimization.type))}>
                          {selectedNode.optimization.type === 'prompt' ? 'Prompt优化' :
                           selectedNode.optimization.type === 'parameter' ? '参数优化' : '模型优化'}
                        </Badge>
                      </div>
                      
                      <p className="text-sm">{selectedNode.optimization.description}</p>
                      
                      <div className="grid grid-cols-3 gap-2 text-xs">
                        <div className="text-center p-2 bg-red-50 rounded">
                          <div className="font-medium">优化前</div>
                          <div>{selectedNode.optimization.performance.before}s</div>
                        </div>
                        <div className="text-center p-2 bg-green-50 rounded">
                          <div className="font-medium">优化后</div>
                          <div>{selectedNode.optimization.performance.after}s</div>
                        </div>
                        <div className="text-center p-2 bg-blue-50 rounded">
                          <div className="font-medium">提升</div>
                          <div className="text-green-600">{selectedNode.optimization.performance.improvement}</div>
                        </div>
                      </div>
                    </Card>
                  )}

                  {selectedNode.isOptimal && (
                    <Card className="p-4 bg-green-50 border-green-200">
                      <div className="flex items-center gap-2 text-green-700">
                        <Zap className="h-4 w-4" />
                        <span className="font-medium">此节点已达到最优状态</span>
                      </div>
                      <p className="text-sm text-green-600 mt-1">
                        当前配置已经是最佳选择，无需进一步优化
                      </p>
                    </Card>
                  )}
                </TabsContent>
                
                <TabsContent value="testing" className="space-y-4">
                  <Card className="p-4 space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="font-semibold">输入调试</h4>
                      <Button
                        size="sm"
                        onClick={handleTestOutput}
                        disabled={isTestingOutput || !customInput.trim()}
                      >
                        {isTestingOutput ? (
                          <RefreshCw className="h-3 w-3 animate-spin mr-1" />
                        ) : (
                          <Play className="h-3 w-3 mr-1" />
                        )}
                        测试
                      </Button>
                    </div>
                    
                    <div className="space-y-2">
                      <label className="text-sm font-medium">自定义输入</label>
                      <Textarea
                        placeholder="输入要测试的内容..."
                        value={customInput}
                        onChange={(e) => setCustomInput(e.target.value)}
                        rows={4}
                      />
                    </div>

                    {selectedNode.optimizedInput && (
                      <div className="space-y-2">
                        <label className="text-sm font-medium">推荐最佳输入</label>
                        <div className="text-xs bg-muted p-2 rounded">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setCustomInput(JSON.stringify(selectedNode.optimizedInput, null, 2))}
                            className="mb-2"
                          >
                            使用推荐输入
                          </Button>
                          <pre className="whitespace-pre-wrap">
                            {JSON.stringify(selectedNode.optimizedInput, null, 2)}
                          </pre>
                        </div>
                      </div>
                    )}
                  </Card>

                  {testOutput && (
                    <Card className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-semibold">测试输出</h4>
                        <Button size="sm" variant="outline">
                          <Download className="h-3 w-3 mr-1" />
                          导出
                        </Button>
                      </div>
                      <div className="text-xs bg-green-50 p-3 rounded border">
                        <pre className="whitespace-pre-wrap">
                          {JSON.stringify(testOutput, null, 2)}
                        </pre>
                      </div>
                    </Card>
                  )}
                </TabsContent>
              </Tabs>
            ) : (
              <div className="space-y-4">
                <div className="text-center text-muted-foreground">
                  <Zap className="h-12 w-12 mx-auto mb-2 text-yellow-500" />
                  <p>选择一个节点查看优化建议和调试工具</p>
                </div>
                
                <Card className="p-4">
                  <h4 className="font-semibold mb-3">优化汇总</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>总节点数量:</span>
                      <span className="font-medium">{optimizationStats.total}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>已达最优:</span>
                      <span className="font-medium text-green-600">{optimizationStats.optimal}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>可以优化:</span>
                      <span className="font-medium text-blue-600">{optimizationStats.optimized}</span>
                    </div>
                  </div>
                </Card>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};