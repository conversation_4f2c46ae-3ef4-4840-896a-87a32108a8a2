import { useState } from 'react';
import { Ch<PERSON>ronDown, ChevronRight, Clock, Zap, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';
import { TraceNode } from '@/types/trace';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface TraceTreeViewProps {
  rootNode: TraceNode;
  selectedNode: TraceNode | null;
  onNodeSelect: (node: TraceNode) => void;
  searchQuery?: string;
}

interface TreeNodeProps {
  node: TraceNode;
  isSelected: boolean;
  onSelect: (node: TraceNode) => void;
  level: number;
  filter?: (node: TraceNode) => boolean;
  selectedNode?: TraceNode | null;
}

const TreeNode = ({ node, isSelected, onSelect, level, filter, selectedNode }: TreeNodeProps) => {
  const [isExpanded, setIsExpanded] = useState(level < 2); // Expand first two levels by default
  const hasChildren = node.children.length > 0;

  const getNodeIcon = (nodeType: string, status: string) => {
    if (status === 'error') return <XCircle className="h-4 w-4 text-trace-error" />;
    if (status === 'pending') return <AlertTriangle className="h-4 w-4 text-trace-warning" />;
    
    switch (nodeType) {
      case 'llm': return <Zap className="h-4 w-4 text-node-llm" />;
      case 'agent': return <CheckCircle className="h-4 w-4 text-node-agent" />;
      case 'tool': return <Clock className="h-4 w-4 text-node-tool" />;
      case 'span': return <Clock className="h-4 w-4 text-node-span" />;
      case 'generation': return <Zap className="h-4 w-4 text-node-generation" />;
      default: return <CheckCircle className="h-4 w-4 text-trace-success" />;
    }
  };

  const getNodeTypeColor = (nodeType: string) => {
    switch (nodeType) {
      case 'llm': return 'bg-node-llm/10 text-node-llm border-node-llm/20';
      case 'agent': return 'bg-node-agent/10 text-node-agent border-node-agent/20';
      case 'tool': return 'bg-node-tool/10 text-node-tool border-node-tool/20';
      case 'span': return 'bg-node-span/10 text-node-span border-node-span/20';
      case 'generation': return 'bg-node-generation/10 text-node-generation border-node-generation/20';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const formatDuration = (ms?: number) => {
    if (!ms) return '-';
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(2)}s`;
    return `${(ms / 60000).toFixed(2)}min`;
  };

  return (
    <div className="select-none">
      <div
        className={cn(
          "flex items-center gap-2 p-2 rounded-md hover:bg-muted/50 cursor-pointer transition-colors",
          isSelected && "bg-trace-accent/10 border border-trace-accent/20"
        )}
        style={{ paddingLeft: `${level * 20 + 8}px` }}
        onClick={() => onSelect(node)}
      >
        {hasChildren ? (
          <Button
            variant="ghost"
            size="sm"
            className="h-4 w-4 p-0 hover:bg-transparent"
            onClick={(e) => {
              e.stopPropagation();
              setIsExpanded(!isExpanded);
            }}
          >
            {isExpanded ? (
              <ChevronDown className="h-3 w-3" />
            ) : (
              <ChevronRight className="h-3 w-3" />
            )}
          </Button>
        ) : (
          <div className="w-4" />
        )}

        {getNodeIcon(node.nodeType, node.status)}

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <span className="font-medium text-sm truncate">{node.name}</span>
            <Badge className={cn("text-xs", getNodeTypeColor(node.nodeType))}>
              {node.nodeType}
            </Badge>
          </div>
          
          <div className="flex items-center gap-4 text-xs text-muted-foreground mt-1">
            <span>{formatDuration(node.duration)}</span>
            {node.tokens && (
              <span className="flex items-center gap-1">
                <Zap className="h-3 w-3" />
                {node.tokens.total}
              </span>
            )}
            {node.model && (
              <span className="font-mono">{node.model}</span>
            )}
          </div>
        </div>
      </div>

      {hasChildren && isExpanded && (
        <div>
          {node.children
            .filter(child => !filter || filter(child))
            .map((child) => (
              <TreeNode
                key={child.id}
                node={child}
                isSelected={selectedNode?.id === child.id}
                onSelect={onSelect}
                level={level + 1}
                filter={filter}
                selectedNode={selectedNode}
              />
            ))}
        </div>
      )}
    </div>
  );
};

export const TraceTreeView = ({ rootNode, selectedNode, onNodeSelect, searchQuery = '' }: TraceTreeViewProps) => {
  const filterNodes = (node: TraceNode): boolean => {
    if (!searchQuery) return true;
    return node.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
           node.nodeType.toLowerCase().includes(searchQuery.toLowerCase());
  };

  return (
    <div className="space-y-1">
      {searchQuery && (
        <div className="text-sm text-muted-foreground mb-4">
          搜索: "{searchQuery}"
        </div>
      )}
      <TreeNode
        node={rootNode}
        isSelected={selectedNode?.id === rootNode.id}
        onSelect={onNodeSelect}
        level={0}
        filter={filterNodes}
        selectedNode={selectedNode}
      />
    </div>
  );
};