import { Clock, User, Hash, Tag, DollarSign, Zap } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { TraceData } from '@/types/trace';

interface TraceHeaderProps {
  trace: TraceData;
}

export const TraceHeader = ({ trace }: TraceHeaderProps) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'bg-trace-success/10 text-trace-success border-trace-success/20';
      case 'error': return 'bg-trace-error/10 text-trace-error border-trace-error/20';
      case 'pending': return 'bg-trace-warning/10 text-trace-warning border-trace-warning/20';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(2)}s`;
    return `${(ms / 60000).toFixed(2)}min`;
  };

  const formatCost = (cost: number) => {
    return cost < 0.01 ? `$${cost.toFixed(6)}` : `$${cost.toFixed(4)}`;
  };

  return (
    <Card className="p-6 border-b">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h2 className="text-2xl font-semibold text-trace-primary">{trace.name}</h2>
            <p className="text-sm text-muted-foreground">Trace ID: {trace.id}</p>
          </div>
          <Badge className={getStatusColor(trace.status)}>
            {trace.status.toUpperCase()}
          </Badge>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <div className="text-sm">
              <p className="font-medium">{formatDuration(trace.duration)}</p>
              <p className="text-muted-foreground">持续时间</p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Zap className="h-4 w-4 text-muted-foreground" />
            <div className="text-sm">
              <p className="font-medium">{trace.totalTokens.toLocaleString()}</p>
              <p className="text-muted-foreground">总 Tokens</p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <DollarSign className="h-4 w-4 text-muted-foreground" />
            <div className="text-sm">
              <p className="font-medium">{formatCost(trace.totalCost)}</p>
              <p className="text-muted-foreground">成本</p>
            </div>
          </div>

          {trace.userId && (
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <div className="text-sm">
                <p className="font-medium">{trace.userId}</p>
                <p className="text-muted-foreground">用户ID</p>
              </div>
            </div>
          )}

          {trace.sessionId && (
            <div className="flex items-center gap-2">
              <Hash className="h-4 w-4 text-muted-foreground" />
              <div className="text-sm">
                <p className="font-medium">{trace.sessionId}</p>
                <p className="text-muted-foreground">会话ID</p>
              </div>
            </div>
          )}

          <div className="flex items-center gap-2">
            <Tag className="h-4 w-4 text-muted-foreground" />
            <div className="text-sm">
              <p className="font-medium">{new Date(trace.timestamp).toLocaleTimeString()}</p>
              <p className="text-muted-foreground">时间戳</p>
            </div>
          </div>
        </div>

        {trace.tags && trace.tags.length > 0 && (
          <div className="flex items-center gap-2 flex-wrap">
            <span className="text-sm text-muted-foreground">标签:</span>
            {trace.tags.map((tag) => (
              <Badge key={tag} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        )}
      </div>
    </Card>
  );
};