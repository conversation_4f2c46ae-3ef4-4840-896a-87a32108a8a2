import { useState } from 'react';
import { Search, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { SAMPLE_TRACE_IDS } from '@/data/mockTraceData';

interface TraceInputProps {
  onTraceLoad: (traceId: string) => void;
  isLoading: boolean;
}

export const TraceInput = ({ onTraceLoad, isLoading }: TraceInputProps) => {
  const [traceId, setTraceId] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (traceId.trim()) {
      onTraceLoad(traceId.trim());
    }
  };

  const handleSampleTrace = (sampleId: string) => {
    setTraceId(sampleId);
    onTraceLoad(sampleId);
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center space-y-6">
        <div className="space-y-2">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
            AI Agent 链路追踪分析
          </h1>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            输入 Trace ID 来分析 AI Agent 的调用链路和性能指标，获得深入的性能洞察和优化建议
          </p>
        </div>
        
        <div className="flex items-center justify-center gap-8 text-sm text-muted-foreground">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span>链路信息获取</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
            <span>BadCase分析</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>AI调优建议</span>
          </div>
        </div>
      </div>

      <Card className="p-8 bg-gradient-to-br from-card to-card/50 border-2">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <label className="text-sm font-medium">Trace ID</label>
            <div className="flex gap-3">
              <Input
                value={traceId}
                onChange={(e) => setTraceId(e.target.value)}
                placeholder="输入 Trace ID (例如: trace_12345678901234567890)"
                className="flex-1 h-12"
                disabled={isLoading}
              />
              <Button 
                type="submit" 
                disabled={!traceId.trim() || isLoading}
                className="min-w-32 h-12 bg-gradient-to-r from-primary to-primary/80"
                size="lg"
              >
                {isLoading ? (
                  <Loader2 className="h-5 w-5 animate-spin" />
                ) : (
                  <>
                    <Search className="h-5 w-5 mr-2" />
                    开始分析
                  </>
                )}
              </Button>
            </div>
          </div>
        </form>

        <div className="mt-8">
          <h3 className="text-sm font-medium text-muted-foreground mb-4">
            或选择示例 Trace ID 快速体验:
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {SAMPLE_TRACE_IDS.map((id, index) => (
              <Button
                key={id}
                variant="outline"
                size="sm"
                onClick={() => handleSampleTrace(id)}
                className="justify-start text-xs font-mono p-3 h-auto hover:bg-primary/5"
                disabled={isLoading}
              >
                <div className="text-left">
                  <div className="font-medium">示例 {index + 1}</div>
                  <div className="text-muted-foreground truncate">
                    {id.slice(0, 24)}...
                  </div>
                </div>
              </Button>
            ))}
          </div>
        </div>
      </Card>
    </div>
  );
};