import { useState } from 'react';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { But<PERSON> } from '@/components/ui/button';
import { TreePine, Network, ArrowLeft, Search, Filter, Maximize2, Download, SidebarClose, SidebarOpen, Loader2, CheckCircle } from 'lucide-react';
import { TraceData, TraceNode } from '@/types/trace';
import { TraceHeader } from './TraceHeader';
import { TraceTreeView } from './TraceTreeView';
import { TraceGraphView } from './TraceGraphView';
import { NodeDetailPanel } from './NodeDetailPanel';
import { Input } from '@/components/ui/input';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';

interface TraceViewerProps {
  trace: TraceData;
  onBack: () => void;
  analysisSession?: any; // 后台分析进度
}

export const TraceViewer = ({ trace, onBack, analysisSession }: TraceViewerProps) => {
  const [viewMode, setViewMode] = useState<'tree' | 'graph'>('tree');
  const [selectedNode, setSelectedNode] = useState<TraceNode | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showDetails, setShowDetails] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);

  return (
    <div className={`${isFullscreen ? 'fixed inset-0 z-50 bg-background' : 'h-[calc(100vh-4rem)]'} flex flex-col`}>
      {/* 工具栏 */}
      <div className="flex-none border-b bg-card/50">
        <div className="flex items-center justify-between p-3">
          {/* 左侧控制区 */}
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={onBack} className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              返回分析步骤
            </Button>
            
            <div className="h-4 w-px bg-border" />
            
            <ToggleGroup 
              type="single" 
              value={viewMode} 
              onValueChange={(value) => value && setViewMode(value as 'tree' | 'graph')}
              className="border rounded-md"
              size="sm"
            >
              <ToggleGroupItem value="tree" aria-label="树形视图" className="flex items-center gap-2 text-sm">
                <TreePine className="h-4 w-4" />
                树形视图
              </ToggleGroupItem>
              <ToggleGroupItem value="graph" aria-label="节点图" className="flex items-center gap-2 text-sm">
                <Network className="h-4 w-4" />
                节点图
              </ToggleGroupItem>
            </ToggleGroup>
          </div>

          {/* 中间搜索区 */}
          <div className="flex-1 max-w-md mx-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索节点..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 h-9"
              />
            </div>
          </div>

          {/* 右侧操作区 */}
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm">
              <Filter className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <Download className="h-4 w-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
            >
              {showDetails ? <SidebarClose className="h-4 w-4" /> : <SidebarOpen className="h-4 w-4" />}
            </Button>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => setIsFullscreen(!isFullscreen)}
            >
              <Maximize2 className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <TraceHeader trace={trace} />
        
        {/* 后台分析进度提示 */}
        {analysisSession && (
          <div className="border-b bg-blue-50/50 p-3">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                {analysisSession.steps.every((step: any) => step.status === 'completed') ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                )}
                <span className="text-sm font-medium text-blue-700">
                  {analysisSession.steps.every((step: any) => step.status === 'completed') ? '分析已完成' : '后台分析进行中'}
                </span>
              </div>
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                {analysisSession.steps?.map((step: any, index: number) => (
                  <div key={step.id} className="flex items-center gap-1">
                    {step.status === 'completed' ? (
                      <CheckCircle className="h-3 w-3 text-green-500" />
                    ) : step.status === 'loading' ? (
                      <Loader2 className="h-3 w-3 animate-spin text-blue-500" />
                    ) : (
                      <div className="h-3 w-3 rounded-full border border-gray-300" />
                    )}
                    <span className={step.status === 'completed' ? 'text-green-600' : step.status === 'loading' ? 'text-blue-600' : 'text-gray-500'}>
                      {step.name}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 主内容区域 - 使用可调节面板 */}
      <div className="flex-1 min-h-0">
        <PanelGroup direction="horizontal">
          {/* 左侧主视图面板 */}
          <Panel defaultSize={showDetails ? 70 : 100} minSize={30}>
            <div className="h-full flex flex-col">
              <div className="flex-1 overflow-auto p-4">
                {viewMode === 'tree' ? (
                  <TraceTreeView
                    rootNode={trace.rootNode}
                    selectedNode={selectedNode}
                    onNodeSelect={setSelectedNode}
                    searchQuery={searchQuery}
                  />
                ) : (
                  <TraceGraphView
                    rootNode={trace.rootNode}
                    selectedNode={selectedNode}
                    onNodeSelect={setSelectedNode}
                    searchQuery={searchQuery}
                  />
                )}
              </div>
            </div>
          </Panel>

          {/* 面板分隔符 */}
          {showDetails && (
            <>
              <PanelResizeHandle className="w-2 bg-border hover:bg-border-hover transition-colors cursor-col-resize" />
              
              {/* 右侧详情面板 */}
              <Panel defaultSize={30} minSize={25} maxSize={50}>
                <div className="h-full border-l bg-card/20">
                  <div className="p-4 border-b">
                    <h3 className="font-medium text-sm text-muted-foreground">节点详情</h3>
                  </div>
                  <div className="flex-1 overflow-auto p-4">
                    <NodeDetailPanel node={selectedNode} />
                  </div>
                </div>
              </Panel>
            </>
          )}
        </PanelGroup>
      </div>
    </div>
  );
};