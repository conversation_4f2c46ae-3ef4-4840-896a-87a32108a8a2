import { <PERSON><PERSON>, Clock, User, Hash, Tag, Zap, DollarSign, <PERSON>, Eye } from 'lucide-react';
import { TraceNode } from '@/types/trace';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';

interface NodeDetailPanelProps {
  node: TraceNode | null;
}

export const NodeDetailPanel = ({ node }: NodeDetailPanelProps) => {
  const { toast } = useToast();

  if (!node) {
    return (
      <Card className="h-full">
        <CardContent className="flex items-center justify-center h-full">
          <div className="text-center text-muted-foreground">
            <Eye className="h-8 w-8 mx-auto mb-2" />
            <p>选择一个节点查看详细信息</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getNodeTypeColor = (nodeType: string) => {
    switch (nodeType) {
      case 'llm': return 'bg-node-llm/10 text-node-llm border-node-llm/20';
      case 'agent': return 'bg-node-agent/10 text-node-agent border-node-agent/20';
      case 'tool': return 'bg-node-tool/10 text-node-tool border-node-tool/20';
      case 'span': return 'bg-node-span/10 text-node-span border-node-span/20';
      case 'generation': return 'bg-node-generation/10 text-node-generation border-node-generation/20';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'bg-trace-success/10 text-trace-success border-trace-success/20';
      case 'error': return 'bg-trace-error/10 text-trace-error border-trace-error/20';
      case 'pending': return 'bg-trace-warning/10 text-trace-warning border-trace-warning/20';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const formatDuration = (ms?: number) => {
    if (!ms) return '-';
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(2)}s`;
    return `${(ms / 60000).toFixed(2)}min`;
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "已复制",
      description: `${label} 已复制到剪贴板`,
    });
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }) + '.' + String(date.getMilliseconds()).padStart(3, '0');
  };

  const formatJson = (obj: any) => {
    if (!obj) return 'null';
    return JSON.stringify(obj, null, 2);
  };

  const calculateCost = (tokens?: { total: number }) => {
    if (!tokens) return 0;
    return tokens.total * 0.00002; // Example pricing
  };

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">{node.name}</CardTitle>
          <div className="flex gap-2">
            <Badge className={getNodeTypeColor(node.nodeType)}>
              {node.nodeType}
            </Badge>
            <Badge className={getStatusColor(node.status)}>
              {node.status}
            </Badge>
          </div>
        </div>
        
        <div className="flex items-center gap-1">
          <span className="text-sm text-muted-foreground font-mono">{node.id}</span>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={() => copyToClipboard(node.id, 'Node ID')}
          >
            <Copy className="h-3 w-3" />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="flex-1 overflow-auto space-y-4">
        {/* Basic Information */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">持续时间</span>
            </div>
            <p className="text-lg font-mono">{formatDuration(node.duration)}</p>
          </div>

          {node.tokens && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Tokens</span>
              </div>
              <div className="space-y-1">
                <p className="text-lg font-mono">{node.tokens.total}</p>
                <p className="text-xs text-muted-foreground">
                  输入: {node.tokens.prompt} | 输出: {node.tokens.completion}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Timestamps */}
        <div className="space-y-2">
          <h4 className="font-medium flex items-center gap-2">
            <Tag className="h-4 w-4" />
            时间信息
          </h4>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">开始时间:</span>
              <span className="font-mono">{formatTimestamp(node.startTime)}</span>
            </div>
            {node.endTime && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">结束时间:</span>
                <span className="font-mono">{formatTimestamp(node.endTime)}</span>
              </div>
            )}
          </div>
        </div>

        {/* Model and Cost Information */}
        {(node.model || node.tokens) && (
          <>
            <Separator />
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                成本信息
              </h4>
              <div className="space-y-1 text-sm">
                {node.model && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">模型:</span>
                    <span className="font-mono">{node.model}</span>
                  </div>
                )}
                {node.tokens && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">预估成本:</span>
                    <span className="font-mono">${calculateCost(node.tokens).toFixed(6)}</span>
                  </div>
                )}
              </div>
            </div>
          </>
        )}

        {/* Metadata */}
        {node.metadata && Object.keys(node.metadata).length > 0 && (
          <>
            <Separator />
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <Hash className="h-4 w-4" />
                元数据
              </h4>
              <div className="space-y-1 text-sm">
                {Object.entries(node.metadata).map(([key, value]) => (
                  <div key={key} className="flex justify-between">
                    <span className="text-muted-foreground">{key}:</span>
                    <span className="font-mono">{String(value)}</span>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

        {/* Input/Output Data */}
        {(node.input || node.output) && (
          <>
            <Separator />
            <Tabs defaultValue="input" className="flex-1">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="input">输入数据</TabsTrigger>
                <TabsTrigger value="output">输出数据</TabsTrigger>
              </TabsList>
              
              <TabsContent value="input" className="mt-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h5 className="font-medium flex items-center gap-2">
                      <Code className="h-4 w-4" />
                      输入
                    </h5>
                    {node.input && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(formatJson(node.input), '输入数据')}
                      >
                        <Copy className="h-3 w-3 mr-1" />
                        复制
                      </Button>
                    )}
                  </div>
                  <pre className="bg-muted p-3 rounded text-xs overflow-auto max-h-60 whitespace-pre-wrap">
                    {formatJson(node.input)}
                  </pre>
                </div>
              </TabsContent>
              
              <TabsContent value="output" className="mt-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h5 className="font-medium flex items-center gap-2">
                      <Code className="h-4 w-4" />
                      输出
                    </h5>
                    {node.output && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(formatJson(node.output), '输出数据')}
                      >
                        <Copy className="h-3 w-3 mr-1" />
                        复制
                      </Button>
                    )}
                  </div>
                  <pre className="bg-muted p-3 rounded text-xs overflow-auto max-h-60 whitespace-pre-wrap">
                    {formatJson(node.output)}
                  </pre>
                </div>
              </TabsContent>
            </Tabs>
          </>
        )}
      </CardContent>
    </Card>
  );
};