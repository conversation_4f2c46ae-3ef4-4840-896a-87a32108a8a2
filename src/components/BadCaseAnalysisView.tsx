import { useState } from 'react';
import { ArrowLeft, Search, Filter, AlertTriangle, TrendingDown, Bug, Zap, Loader2, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { TraceData } from '@/types/trace';
import { BadCaseNode } from '@/types/analysis';
import { TraceGraphView } from './TraceGraphView';
import { NodeDetailPanel } from './NodeDetailPanel';
import { cn } from '@/lib/utils';

interface BadCaseAnalysisViewProps {
  trace: TraceData;
  onBack: () => void;
  analysisSession?: any;
}

export const BadCaseAnalysisView = ({ trace, onBack, analysisSession }: BadCaseAnalysisViewProps) => {
  const [selectedNode, setSelectedNode] = useState<BadCaseNode | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [severityFilter, setSeverityFilter] = useState<'all' | 'high' | 'medium' | 'low'>('all');

  // 模拟添加BadCase信息到节点
  const addBadCaseInfo = (node: any): BadCaseNode => {
    const badCases = [
      {
        type: 'performance' as const,
        severity: 'high' as const,
        rootCause: '模型响应时间过长，超过3秒阈值',
        suggestion: '考虑使用更快的模型或优化prompt长度'
      },
      {
        type: 'error' as const,
        severity: 'medium' as const,
        rootCause: 'API调用失败，返回错误状态码',
        suggestion: '增加重试机制和错误处理逻辑'
      },
      {
        type: 'accuracy' as const,
        severity: 'low' as const,
        rootCause: '输出格式不符合预期',
        suggestion: '优化prompt指令，增加格式约束'
      }
    ];

    const shouldHaveBadCase = Math.random() > 0.6; // 40%的节点有BadCase
    
    return {
      ...node,
      badCaseInfo: shouldHaveBadCase ? badCases[Math.floor(Math.random() * badCases.length)] : undefined,
      children: node.children?.map(addBadCaseInfo) || []
    };
  };

  const enhancedRootNode = addBadCaseInfo(trace.rootNode);

  const getBadCaseStats = (node: BadCaseNode): { total: number; high: number; medium: number; low: number } => {
    let stats = { total: 0, high: 0, medium: 0, low: 0 };
    
    if (node.badCaseInfo) {
      stats.total++;
      stats[node.badCaseInfo.severity]++;
    }
    
    node.children?.forEach(child => {
      const childStats = getBadCaseStats(child);
      stats.total += childStats.total;
      stats.high += childStats.high;
      stats.medium += childStats.medium;
      stats.low += childStats.low;
    });
    
    return stats;
  };

  const badCaseStats = getBadCaseStats(enhancedRootNode);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'performance': return '⚡';
      case 'error': return '❌';
      case 'accuracy': return '🎯';
      default: return '⚠️';
    }
  };

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* 头部工具栏 */}
      <div className="flex-none border-b bg-card/50">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={onBack} className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              返回分析流程
            </Button>
            
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-500" />
              <h1 className="text-lg font-semibold">BadCase根因分析</h1>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <div className="flex gap-2">
              <Badge variant="destructive">{badCaseStats.high} 高风险</Badge>
              <Badge variant="outline" className="text-yellow-600">{badCaseStats.medium} 中风险</Badge>
              <Badge variant="outline" className="text-blue-600">{badCaseStats.low} 低风险</Badge>
            </div>
          </div>
        </div>

        {/* 搜索和过滤 */}
        <div className="px-4 pb-4 flex items-center gap-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索BadCase节点..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="flex gap-2">
            {['all', 'high', 'medium', 'low'].map((severity) => (
              <Button
                key={severity}
                variant={severityFilter === severity ? "default" : "outline"}
                size="sm"
                onClick={() => setSeverityFilter(severity as any)}
              >
                {severity === 'all' ? '全部' : 
                 severity === 'high' ? '高风险' :
                 severity === 'medium' ? '中风险' : '低风险'}
              </Button>
            ))}
          </div>
        </div>
        
        {/* 后台分析进度提示 */}
        {analysisSession && (
          <div className="border-t bg-blue-50/50 p-3">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                {analysisSession.steps.every((step: any) => step.status === 'completed') ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                )}
                <span className="text-sm font-medium text-blue-700">
                  {analysisSession.steps.every((step: any) => step.status === 'completed') ? '分析已完成' : '后台分析进行中'}
                </span>
              </div>
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                {analysisSession.steps?.map((step: any, index: number) => (
                  <div key={step.id} className="flex items-center gap-1">
                    {step.status === 'completed' ? (
                      <CheckCircle className="h-3 w-3 text-green-500" />
                    ) : step.status === 'loading' ? (
                      <Loader2 className="h-3 w-3 animate-spin text-blue-500" />
                    ) : (
                      <div className="h-3 w-3 rounded-full border border-gray-300" />
                    )}
                    <span className={step.status === 'completed' ? 'text-green-600' : step.status === 'loading' ? 'text-blue-600' : 'text-gray-500'}>
                      {step.name}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 主内容区域 */}
      <div className="flex-1 flex min-h-0">
        {/* 图形视图 */}
        <div className="flex-1 p-4">
          <TraceGraphView
            rootNode={enhancedRootNode as any}
            selectedNode={selectedNode as any}
            onNodeSelect={(node) => setSelectedNode(node as BadCaseNode)}
            searchQuery={searchQuery}
          />
        </div>

        {/* 详情面板 */}
        <div className="w-96 border-l bg-card/20 flex flex-col">
          <div className="p-4 border-b">
            <h3 className="font-medium">
              {selectedNode ? 'BadCase详情' : 'BadCase概览'}
            </h3>
          </div>
          
          <div className="flex-1 overflow-auto p-4">
            {selectedNode ? (
              <div className="space-y-4">
                <NodeDetailPanel node={selectedNode as any} />
                
                {selectedNode.badCaseInfo && (
                  <Card className="p-4 space-y-3">
                    <div className="flex items-center gap-2">
                      <span className="text-xl">{getTypeIcon(selectedNode.badCaseInfo.type)}</span>
                      <h4 className="font-semibold">BadCase分析</h4>
                      <Badge className={cn("text-xs", getSeverityColor(selectedNode.badCaseInfo.severity))}>
                        {selectedNode.badCaseInfo.severity === 'high' ? '高风险' :
                         selectedNode.badCaseInfo.severity === 'medium' ? '中风险' : '低风险'}
                      </Badge>
                    </div>
                    
                    <div className="space-y-2">
                      <div>
                        <h5 className="text-sm font-medium text-muted-foreground">根因分析</h5>
                        <p className="text-sm">{selectedNode.badCaseInfo.rootCause}</p>
                      </div>
                      
                      <div>
                        <h5 className="text-sm font-medium text-muted-foreground">优化建议</h5>
                        <p className="text-sm">{selectedNode.badCaseInfo.suggestion}</p>
                      </div>
                    </div>
                  </Card>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                <div className="text-center text-muted-foreground">
                  <AlertTriangle className="h-12 w-12 mx-auto mb-2 text-amber-500" />
                  <p>选择一个节点查看详细的BadCase分析</p>
                </div>
                
                <Card className="p-4">
                  <h4 className="font-semibold mb-3">分析汇总</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>总BadCase数量:</span>
                      <span className="font-medium">{badCaseStats.total}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>高风险问题:</span>
                      <span className="font-medium text-red-600">{badCaseStats.high}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>中风险问题:</span>
                      <span className="font-medium text-yellow-600">{badCaseStats.medium}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>低风险问题:</span>
                      <span className="font-medium text-blue-600">{badCaseStats.low}</span>
                    </div>
                  </div>
                </Card>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};