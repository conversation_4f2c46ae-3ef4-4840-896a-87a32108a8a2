export interface AnalysisStep {
  id: 'trace-info' | 'badcase-analysis' | 'ai-optimization';
  name: string;
  description: string;
  status: 'pending' | 'loading' | 'completed' | 'error';
  completedAt?: string;
}

export interface AnalysisSession {
  traceId: string;
  steps: AnalysisStep[];
  startedAt: string;
  currentStep: number;
}

import { TraceNode } from './trace';

export interface BadCaseNode extends TraceNode {
  badCaseInfo?: {
    type: 'performance' | 'error' | 'accuracy';
    severity: 'low' | 'medium' | 'high';
    rootCause: string;
    suggestion: string;
  };
  children: BadCaseNode[];
}

export interface OptimizedNode extends TraceNode {
  optimizedInput?: any;
  optimizedOutput?: any;
  isOptimal?: boolean;
  optimization?: {
    type: 'prompt' | 'parameter' | 'model';
    description: string;
    performance: {
      before: number;
      after: number;
      improvement: string;
    };
  };
  children: OptimizedNode[];
}