export interface TraceNode {
  id: string;
  name: string;
  type: 'trace' | 'span' | 'generation' | 'event';
  nodeType: 'llm' | 'agent' | 'tool' | 'span' | 'generation';
  status: 'success' | 'error' | 'pending';
  startTime: string;
  endTime?: string;
  duration?: number;
  input?: any;
  output?: any;
  metadata?: Record<string, any>;
  children: TraceNode[];
  parent?: string;
  level: number;
  tokens?: {
    prompt: number;
    completion: number;
    total: number;
  };
  model?: string;
  version?: string;
  userId?: string;
  sessionId?: string;
  tags?: string[];
}

export interface TraceData {
  id: string;
  name: string;
  userId?: string;
  sessionId?: string;
  timestamp: string;
  duration: number;
  status: 'success' | 'error' | 'pending';
  input?: any;
  output?: any;
  metadata?: Record<string, any>;
  rootNode: TraceNode;
  totalTokens: number;
  totalCost: number;
  tags?: string[];
}

export interface NodePosition {
  x: number;
  y: number;
}

export interface NodeConnection {
  from: string;
  to: string;
}

export interface GraphData {
  nodes: (TraceNode & { position: NodePosition })[];
  connections: NodeConnection[];
}