import { TraceInput } from '@/components/TraceInput';
import { TraceViewer } from '@/components/TraceViewer';
import { AnalysisProgress } from '@/components/AnalysisProgress';
import { BadCaseAnalysisView } from '@/components/BadCaseAnalysisView';
import { AIOptimizationView } from '@/components/AIOptimizationView';
import { useAppStore } from '@/store/useAppStore';
import { useToast } from '@/hooks/use-toast';
import { Activity, Github, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';

const Index = () => {
  const { 
    currentView, 
    isLoading, 
    trace, 
    analysisSession,
    startTraceAnalysis,
    handleStepClick,
    handleBackToInput,
    handleBackToAnalysis
  } = useAppStore();
  const { toast } = useToast();

  const handleTraceLoad = async (traceId: string) => {
    try {
      await startTraceAnalysis(traceId);
      
      toast({
        title: "开始分析",
        description: `正在分析 Trace: ${traceId}`,
      });
    } catch (error) {
      toast({
        title: "分析启动失败",
        description: "无法启动分析流程，请稍后重试",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* 顶部导航栏 */}
      <header className="border-b bg-card/50 backdrop-blur supports-[backdrop-filter]:bg-card/50">
        <div className="flex h-16 items-center justify-between px-6">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <Activity className="h-6 w-6 text-primary" />
              <h1 className="text-xl font-semibold">AI Agent 链路追踪</h1>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm">
              <Settings className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <Github className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </header>

      {/* 主内容区域 */}
      <main className="flex-1">
        {currentView === 'input' ? (
          <div className="h-[calc(100vh-4rem)] flex items-center justify-center p-6">
            <div className="w-full max-w-2xl">
              <TraceInput onTraceLoad={handleTraceLoad} isLoading={isLoading} />
            </div>
          </div>
        ) : currentView === 'analysis' ? (
          <AnalysisProgress />
        ) : currentView === 'trace-info' && trace ? (
          <TraceViewer trace={trace} onBack={handleBackToAnalysis} analysisSession={analysisSession} />
        ) : currentView === 'badcase-analysis' && trace ? (
          <BadCaseAnalysisView trace={trace} onBack={handleBackToAnalysis} analysisSession={analysisSession} />
        ) : currentView === 'ai-optimization' && trace ? (
          <AIOptimizationView trace={trace} onBack={handleBackToAnalysis} analysisSession={analysisSession} />
        ) : null}
      </main>
    </div>
  );
};

export default Index;
