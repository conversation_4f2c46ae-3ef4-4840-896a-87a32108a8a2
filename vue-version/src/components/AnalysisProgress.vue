<template>
  <div class="w-full max-w-4xl mx-auto p-6 space-y-8">
    <!-- 返回首页按钮 -->
    <div class="flex items-center">
      <button 
        @click="handleBackToInput" 
        class="flex items-center gap-2 px-3 py-1.5 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground rounded-md"
      >
        <ArrowLeftIcon class="h-4 w-4" />
        返回首页
      </button>
    </div>
    
    <div class="text-center space-y-6">
      <div class="space-y-2">
        <h2 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          {{ progress === 100 ? '分析已完成' : '分析进行中...' }}
        </h2>
        <p class="text-muted-foreground">
          {{ progress === 100 ? '已完成分析' : '正在分析' }} Trace: 
          <span class="font-mono text-sm bg-muted px-2 py-1 rounded">{{ currentTraceId }}</span>
        </p>
      </div>
      
      <div class="space-y-4">
        <div class="relative">
          <div class="w-full h-3 bg-secondary rounded-full overflow-hidden">
            <div 
              class="h-full bg-primary rounded-full transition-all duration-300 ease-out"
              :style="{ width: `${progress}%` }"
            ></div>
          </div>
          <div class="absolute inset-0 flex items-center justify-center text-xs font-medium text-white mix-blend-difference">
            {{ Math.round(progress) }}%
          </div>
        </div>
        <p class="text-sm text-muted-foreground">
          完成进度: {{ completedStepsCount }} / {{ totalStepsCount }} 步骤
        </p>
      </div>
    </div>

    <div class="space-y-6">
      <div 
        v-for="(step, index) in analysisSession?.steps || []" 
        :key="step.id" 
        :class="[
          'p-6 transition-all duration-300 hover:shadow-lg rounded-lg border cursor-pointer',
          {
            'border-green-200 bg-gradient-to-r from-green-50 to-emerald-50 hover:from-green-100 hover:to-emerald-100': step.status === 'completed',
            'border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 animate-pulse': step.status === 'loading',
            'border-gray-200 bg-gray-50/50': step.status === 'pending'
          }
        ]"
        @click="handleStepClickLocal(step, index)"
      >
        <div class="flex items-center gap-6">
          <div class="flex-shrink-0">
            <div :class="[
              'w-12 h-12 rounded-full flex items-center justify-center',
              {
                'bg-green-100': step.status === 'completed',
                'bg-blue-100': step.status === 'loading',
                'bg-gray-100': step.status === 'pending'
              }
            ]">
              <component :is="getStepIcon(step, index)" />
            </div>
          </div>
          
          <div class="flex-1 min-w-0">
            <div class="flex items-center justify-between">
              <div>
                <h3 class="text-lg font-semibold mb-1">{{ step.name }}</h3>
                <p class="text-sm text-muted-foreground">
                  {{ step.description }}
                </p>
              </div>
              
              <button 
                v-if="step.status === 'completed'"
                class="ml-4 px-3 py-1.5 border border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm"
              >
                <PlayIcon class="h-3 w-3 mr-2" />
                查看详情
              </button>
              
              <button 
                v-if="step.status === 'loading'"
                disabled
                class="ml-4 px-3 py-1.5 border border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm"
              >
                <div class="h-3 w-3 mr-2 animate-spin rounded-full border border-current border-t-transparent"></div>
                分析进行中...
              </button>
            </div>
            
            <div class="mt-3">
              <div v-if="step.status === 'loading'" class="flex items-center gap-2">
                <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                <p class="text-sm text-blue-600 font-medium">
                  正在处理中...
                </p>
              </div>
              
              <div v-if="step.status === 'completed' && step.completedAt" class="flex items-center gap-2">
                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                <p class="text-sm text-green-600 font-medium">
                  已完成 - {{ formatTime(step.completedAt) }}
                </p>
              </div>
              
              <div v-if="step.status === 'pending'" class="flex items-center gap-2">
                <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                <p class="text-sm text-gray-500">
                  等待处理...
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch, ref } from 'vue';
import { useAppStore } from '@/stores/app';
import type { AnalysisStep } from '@/types/analysis';

// Icon components
const CheckIcon = {
  template: `
    <svg class="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <polyline points="20,6 9,17 4,12"/>
    </svg>
  `
};

const LoaderIcon = {
  template: `
    <div class="h-5 w-5 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"></div>
  `
};

const ArrowLeftIcon = {
  template: `
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="m12 19-7-7 7-7"/>
      <path d="M19 12H5"/>
    </svg>
  `
};

const PlayIcon = {
  template: `
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <polygon points="5,3 19,12 5,21"/>
    </svg>
  `
};

const appStore = useAppStore();

const progress = ref(0);

const analysisSession = computed(() => appStore.analysisSession);
const currentTraceId = computed(() => appStore.currentTraceId);

const completedStepsCount = computed(() => {
  if (!analysisSession.value) return 0;
  return analysisSession.value.steps.filter(s => s.status === 'completed').length;
});

const totalStepsCount = computed(() => {
  if (!analysisSession.value) return 0;
  return analysisSession.value.steps.length;
});

// Watch for changes in analysis session to update progress
watch(
  () => analysisSession.value?.steps,
  (steps) => {
    if (!steps) return;
    const completed = steps.filter(s => s.status === 'completed').length;
    progress.value = (completed / steps.length) * 100;
  },
  { deep: true, immediate: true }
);

const handleStepClickLocal = (step: AnalysisStep, index: number) => {
  // 只有完成的步骤才可以点击
  if (step.status === 'completed') {
    appStore.handleStepClick(step.id, true);
  }
};

const handleBackToInput = () => {
  appStore.handleBackToInput();
};

const getStepIcon = (step: AnalysisStep, index: number) => {
  if (step.status === 'completed') {
    return CheckIcon;
  } else if (step.status === 'loading') {
    return LoaderIcon;
  } else {
    return {
      template: `
        <div class="h-5 w-5 rounded-full border-2 flex items-center justify-center text-xs font-medium border-muted text-muted-foreground">
          ${index + 1}
        </div>
      `
    };
  }
};

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString();
};
</script>