<template>
  <div class="h-screen flex flex-col bg-background">
    <!-- 头部工具栏 -->
    <div class="flex-none border-b bg-card/50">
      <div class="flex items-center justify-between p-4">
        <div class="flex items-center gap-4">
          <button 
            @click="$emit('back')" 
            class="flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-md hover:bg-accent hover:text-accent-foreground transition-colors"
          >
            <ArrowLeftIcon />
            返回分析流程
          </button>
          
          <div class="flex items-center gap-2">
            <AlertTriangleIcon class="h-5 w-5 text-amber-500" />
            <h1 class="text-lg font-semibold">BadCase根因分析</h1>
          </div>
        </div>

        <div class="flex items-center gap-4">
          <div class="flex gap-2">
            <span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium bg-red-100 text-red-800">
              {{ badCaseStats.high }} 高风险
            </span>
            <span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium border border-gray-200 text-yellow-600">
              {{ badCaseStats.medium }} 中风险
            </span>
            <span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium border border-gray-200 text-blue-600">
              {{ badCaseStats.low }} 低风险
            </span>
          </div>
        </div>
      </div>

      <!-- 搜索和过滤 -->
      <div class="px-4 pb-4 flex items-center gap-4">
        <div class="relative flex-1 max-w-md">
          <SearchIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <input
            v-model="searchQuery"
            placeholder="搜索BadCase节点..."
            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 pl-10 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          />
        </div>
        
        <div class="flex gap-2">
          <button
            v-for="severity in ['all', 'high', 'medium', 'low']"
            :key="severity"
            @click="severityFilter = severity as any"
            :class="[
              'inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-9 px-3',
              severityFilter === severity ? 'bg-primary text-primary-foreground hover:bg-primary/90' : 'border border-input bg-background hover:bg-accent hover:text-accent-foreground'
            ]"
          >
            {{ severity === 'all' ? '全部' : 
               severity === 'high' ? '高风险' :
               severity === 'medium' ? '中风险' : '低风险' }}
          </button>
        </div>
      </div>
      
      <!-- 后台分析进度提示 -->
      <div v-if="analysisSession" class="border-t bg-blue-50/50 p-3">
        <div class="flex items-center gap-4">
          <div class="flex items-center gap-2">
            <CheckCircleIcon v-if="isAnalysisCompleted" class="h-4 w-4 text-green-500" />
            <Loader2Icon v-else class="h-4 w-4 animate-spin text-blue-500" />
            <span class="text-sm font-medium text-blue-700">
              {{ isAnalysisCompleted ? '分析已完成' : '后台分析进行中' }}
            </span>
          </div>
          <div class="flex items-center gap-4 text-xs text-muted-foreground">
            <div 
              v-for="(step, index) in analysisSession.steps" 
              :key="step.id" 
              class="flex items-center gap-1"
            >
              <CheckCircleIcon v-if="step.status === 'completed'" class="h-3 w-3 text-green-500" />
              <Loader2Icon v-else-if="step.status === 'loading'" class="h-3 w-3 animate-spin text-blue-500" />
              <div v-else class="h-3 w-3 rounded-full border border-gray-300" />
              <span :class="{
                'text-green-600': step.status === 'completed',
                'text-blue-600': step.status === 'loading',
                'text-gray-500': step.status === 'pending'
              }">
                {{ step.name }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="flex-1 flex min-h-0">
      <!-- 图形视图 -->
      <div class="flex-1 p-4">
        <div class="h-full flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg">
          <div class="text-center text-gray-500">
            <div class="text-6xl mb-4">🔍</div>
            <p class="text-xl font-medium mb-2">BadCase图形视图</p>
            <p class="text-sm">TraceGraphView组件待实现</p>
          </div>
        </div>
      </div>

      <!-- 详情面板 -->
      <div class="w-96 border-l bg-card/20 flex flex-col">
        <div class="p-4 border-b">
          <h3 class="font-medium">
            {{ selectedNode ? 'BadCase详情' : 'BadCase概览' }}
          </h3>
        </div>
        
        <div class="flex-1 overflow-auto p-4">
          <div v-if="selectedNode" class="space-y-4">
            <!-- 节点详情面板 -->
            <div class="p-4 border rounded-lg bg-card">
              <div class="text-center text-gray-500">
                <div class="text-4xl mb-2">📊</div>
                <p class="text-sm">NodeDetailPanel组件待实现</p>
              </div>
            </div>
            
            <div v-if="selectedNode.badCaseInfo" class="p-4 border rounded-lg bg-card space-y-3">
              <div class="flex items-center gap-2">
                <span class="text-xl">{{ getTypeIcon(selectedNode.badCaseInfo.type) }}</span>
                <h4 class="font-semibold">BadCase分析</h4>
                <span :class="['text-xs px-2.5 py-0.5 rounded-full', getSeverityColor(selectedNode.badCaseInfo.severity)]">
                  {{ selectedNode.badCaseInfo.severity === 'high' ? '高风险' :
                     selectedNode.badCaseInfo.severity === 'medium' ? '中风险' : '低风险' }}
                </span>
              </div>
              
              <div class="space-y-2">
                <div>
                  <h5 class="text-sm font-medium text-muted-foreground">根因分析</h5>
                  <p class="text-sm">{{ selectedNode.badCaseInfo.rootCause }}</p>
                </div>
                
                <div>
                  <h5 class="text-sm font-medium text-muted-foreground">优化建议</h5>
                  <p class="text-sm">{{ selectedNode.badCaseInfo.suggestion }}</p>
                </div>
              </div>
            </div>
          </div>
          
          <div v-else class="space-y-4">
            <div class="text-center text-muted-foreground">
              <AlertTriangleIcon class="h-12 w-12 mx-auto mb-2 text-amber-500" />
              <p>选择一个节点查看详细的BadCase分析</p>
            </div>
            
            <div class="p-4 border rounded-lg bg-card">
              <h4 class="font-semibold mb-3">分析汇总</h4>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span>总BadCase数量:</span>
                  <span class="font-medium">{{ badCaseStats.total }}</span>
                </div>
                <div class="flex justify-between">
                  <span>高风险问题:</span>
                  <span class="font-medium text-red-600">{{ badCaseStats.high }}</span>
                </div>
                <div class="flex justify-between">
                  <span>中风险问题:</span>
                  <span class="font-medium text-yellow-600">{{ badCaseStats.medium }}</span>
                </div>
                <div class="flex justify-between">
                  <span>低风险问题:</span>
                  <span class="font-medium text-blue-600">{{ badCaseStats.low }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { TraceData, TraceNode } from '@/types/trace';

// Icons
const ArrowLeftIcon = {
  template: `
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="m12 19-7-7 7-7"/>
      <path d="M19 12H5"/>
    </svg>
  `
};

const SearchIcon = {
  template: `
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <circle cx="11" cy="11" r="8"/>
      <path d="m21 21-4.35-4.35"/>
    </svg>
  `
};

const AlertTriangleIcon = {
  template: `
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"/>
      <path d="M12 9v4"/>
      <path d="m12 17 .01 0"/>
    </svg>
  `
};

const CheckCircleIcon = {
  template: `
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
      <polyline points="22,4 12,14.01 9,11.01"/>
    </svg>
  `
};

const Loader2Icon = {
  template: `
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M21 12a9 9 0 1 1-6.219-8.56"/>
    </svg>
  `
};

// Types
interface BadCaseInfo {
  type: 'performance' | 'error' | 'accuracy';
  severity: 'high' | 'medium' | 'low';
  rootCause: string;
  suggestion: string;
}

interface BadCaseNode extends TraceNode {
  badCaseInfo?: BadCaseInfo;
  children?: BadCaseNode[];
}

interface Props {
  trace: TraceData;
  analysisSession?: any;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  back: [];
}>();

// Reactive state
const selectedNode = ref<BadCaseNode | null>(null);
const searchQuery = ref('');
const severityFilter = ref<'all' | 'high' | 'medium' | 'low'>('all');

// Computed properties
const isAnalysisCompleted = computed(() => {
  return props.analysisSession?.steps?.every((step: any) => step.status === 'completed') || false;
});

// 模拟添加BadCase信息到节点
const addBadCaseInfo = (node: TraceNode): BadCaseNode => {
  const badCases: BadCaseInfo[] = [
    {
      type: 'performance',
      severity: 'high',
      rootCause: '模型响应时间过长，超过3秒阈值',
      suggestion: '考虑使用更快的模型或优化prompt长度'
    },
    {
      type: 'error',
      severity: 'medium',
      rootCause: 'API调用失败，返回错误状态码',
      suggestion: '增加重试机制和错误处理逻辑'
    },
    {
      type: 'accuracy',
      severity: 'low',
      rootCause: '输出格式不符合预期',
      suggestion: '优化prompt指令，增加格式约束'
    }
  ];

  const shouldHaveBadCase = Math.random() > 0.6; // 40%的节点有BadCase
  
  return {
    ...node,
    badCaseInfo: shouldHaveBadCase ? badCases[Math.floor(Math.random() * badCases.length)] : undefined,
    children: node.children?.map(addBadCaseInfo) || []
  };
};

const enhancedRootNode = computed(() => addBadCaseInfo(props.trace.rootNode));

const getBadCaseStats = (node: BadCaseNode): { total: number; high: number; medium: number; low: number } => {
  let stats = { total: 0, high: 0, medium: 0, low: 0 };
  
  if (node.badCaseInfo) {
    stats.total++;
    stats[node.badCaseInfo.severity]++;
  }
  
  node.children?.forEach(child => {
    const childStats = getBadCaseStats(child);
    stats.total += childStats.total;
    stats.high += childStats.high;
    stats.medium += childStats.medium;
    stats.low += childStats.low;
  });
  
  return stats;
};

const badCaseStats = computed(() => getBadCaseStats(enhancedRootNode.value));

const getSeverityColor = (severity: string) => {
  switch (severity) {
    case 'high': return 'text-red-600 bg-red-100';
    case 'medium': return 'text-yellow-600 bg-yellow-100';
    case 'low': return 'text-blue-600 bg-blue-100';
    default: return 'text-gray-600 bg-gray-100';
  }
};

const getTypeIcon = (type: string) => {
  switch (type) {
    case 'performance': return '⚡';
    case 'error': return '❌';
    case 'accuracy': return '🎯';
    default: return '⚠️';
  }
};
</script>