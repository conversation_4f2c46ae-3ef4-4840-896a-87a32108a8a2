<template>
  <div :class="isFullscreen ? 'fixed inset-0 z-50 bg-background' : 'h-[calc(100vh-4rem)]'" class="flex flex-col">
    <!-- 工具栏 -->
    <div class="flex-none border-b bg-card/50">
      <div class="flex items-center justify-between p-3">
        <!-- 左侧控制区 -->
        <div class="flex items-center gap-4">
          <button 
            @click="$emit('back')"
            class="px-3 py-1.5 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground rounded-md flex items-center gap-2"
          >
            <ArrowLeftIcon class="h-4 w-4" />
            返回分析步骤
          </button>
          
          <div class="h-4 w-px bg-border" />
          
          <div class="border rounded-md flex">
            <button 
              @click="viewMode = 'tree'"
              :class="[
                'flex items-center gap-2 text-sm px-3 py-1.5 rounded-l-md transition-colors',
                viewMode === 'tree' ? 'bg-accent text-accent-foreground' : 'hover:bg-accent/50'
              ]"
            >
              <TreePineIcon class="h-4 w-4" />
              树形视图
            </button>
            <button 
              @click="viewMode = 'graph'"
              :class="[
                'flex items-center gap-2 text-sm px-3 py-1.5 rounded-r-md transition-colors border-l',
                viewMode === 'graph' ? 'bg-accent text-accent-foreground' : 'hover:bg-accent/50'
              ]"
            >
              <NetworkIcon class="h-4 w-4" />
              节点图
            </button>
          </div>
        </div>

        <!-- 中间搜索区 -->
        <div class="flex-1 max-w-md mx-6">
          <div class="relative">
            <SearchIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <input
              v-model="searchQuery"
              placeholder="搜索节点..."
              class="w-full pl-10 h-9 px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 border border-input bg-background rounded-md"
            />
          </div>
        </div>

        <!-- 右侧操作区 -->
        <div class="flex items-center gap-2">
          <button class="px-3 py-1.5 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground rounded-md">
            <FilterIcon class="h-4 w-4" />
          </button>
          <button class="px-3 py-1.5 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground rounded-md">
            <DownloadIcon class="h-4 w-4" />
          </button>
          <button 
            @click="showDetails = !showDetails"
            class="px-3 py-1.5 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground rounded-md"
          >
            <SidebarCloseIcon v-if="showDetails" class="h-4 w-4" />
            <SidebarOpenIcon v-else class="h-4 w-4" />
          </button>
          <button 
            @click="isFullscreen = !isFullscreen"
            class="px-3 py-1.5 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground rounded-md"
          >
            <Maximize2Icon class="h-4 w-4" />
          </button>
        </div>
      </div>

      <TraceHeader :trace="trace" />
      
      <!-- 后台分析进度提示 -->
      <div v-if="analysisSession" class="border-b bg-blue-50/50 p-3">
        <div class="flex items-center gap-4">
          <div class="flex items-center gap-2">
            <CheckCircleIcon v-if="isAnalysisCompleted" class="h-4 w-4 text-green-500" />
            <Loader2Icon v-else class="h-4 w-4 animate-spin text-blue-500" />
            <span class="text-sm font-medium text-blue-700">
              {{ isAnalysisCompleted ? '分析已完成' : '后台分析进行中' }}
            </span>
          </div>
          <div class="flex items-center gap-4 text-xs text-muted-foreground">
            <div 
              v-for="(step, index) in analysisSession.steps" 
              :key="step.id" 
              class="flex items-center gap-1"
            >
              <CheckCircleIcon v-if="step.status === 'completed'" class="h-3 w-3 text-green-500" />
              <Loader2Icon v-else-if="step.status === 'loading'" class="h-3 w-3 animate-spin text-blue-500" />
              <div v-else class="h-3 w-3 rounded-full border border-gray-300" />
              <span :class="{
                'text-green-600': step.status === 'completed',
                'text-blue-600': step.status === 'loading',
                'text-gray-500': step.status === 'pending'
              }">
                {{ step.name }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主内容区域 - 使用可调节面板 -->
    <div class="flex-1 min-h-0">
      <div class="flex h-full">
        <!-- 左侧主视图面板 -->
        <div :class="showDetails ? 'flex-[70]' : 'flex-1'" class="min-w-0">
          <div class="h-full flex flex-col">
            <div class="flex-1 overflow-auto p-4">
              <TraceTreeView
                v-if="viewMode === 'tree'"
                :root-node="trace.rootNode"
                :selected-node="selectedNode"
                :search-query="searchQuery"
                @node-select="setSelectedNode"
              />
              <TraceGraphView
                v-else
                :root-node="trace.rootNode"
                :selected-node="selectedNode"
                :search-query="searchQuery"
                @node-select="setSelectedNode"
              />
            </div>
          </div>
        </div>

        <!-- 右侧详情面板 -->
        <div v-if="showDetails" class="flex-[30] min-w-0 border-l bg-card/20">
          <div class="p-4 border-b">
            <h3 class="font-medium text-sm text-muted-foreground">节点详情</h3>
          </div>
          <div class="flex-1 overflow-auto p-4">
            <NodeDetailPanel :node="selectedNode" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { TraceData, TraceNode } from '@/types/trace';
// These components will be created later - using placeholders for now
// import TraceHeader from './TraceHeader.vue';
// import TraceTreeView from './TraceTreeView.vue';
// import TraceGraphView from './TraceGraphView.vue';
// import NodeDetailPanel from './NodeDetailPanel.vue';

// Temporary placeholder components
const TraceHeader = {
  props: ['trace'],
  template: '<div class="p-4 border-b"><h2 class="text-lg font-semibold">{{ trace.name || trace.traceId }}</h2><p class="text-sm text-muted-foreground">Duration: {{ trace.duration }}ms | Status: {{ trace.status }}</p></div>'
};

const TraceTreeView = {
  props: ['rootNode', 'selectedNode', 'searchQuery'],
  emits: ['node-select'],
  template: '<div class="p-4"><h3>Tree View - Coming Soon</h3><p>Root: {{ rootNode?.name }}</p></div>'
};

const TraceGraphView = {
  props: ['rootNode', 'selectedNode', 'searchQuery'],
  emits: ['node-select'],
  template: '<div class="p-4"><h3>Graph View - Coming Soon</h3><p>Root: {{ rootNode?.name }}</p></div>'
};

const NodeDetailPanel = {
  props: ['node'],
  template: '<div><h4 class="font-medium mb-2">Node Details</h4><div v-if="node"><p><strong>Name:</strong> {{ node.name }}</p><p><strong>Type:</strong> {{ node.type }}</p><p><strong>Status:</strong> {{ node.status }}</p></div><p v-else class="text-muted-foreground">选择一个节点查看详情</p></div>'
};

// Icon components
const ArrowLeftIcon = {
  template: `
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="m12 19-7-7 7-7"/>
      <path d="M19 12H5"/>
    </svg>
  `
};

const TreePineIcon = {
  template: `
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="m17 14 3 3.3a1 1 0 0 1-.7 1.7H4.7a1 1 0 0 1-.7-1.7L7 14h-.3a1 1 0 0 1-.7-1.7L9 9h-.2A1 1 0 0 1 8 7.3L12 3l4 4.3a1 1 0 0 1-.8 1.7H15l3 3.3a1 1 0 0 1-.7 1.7H17Z"/>
      <path d="M12 22V3"/>
    </svg>
  `
};

const NetworkIcon = {
  template: `
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <rect x="16" y="16" width="6" height="6" rx="1"/>
      <rect x="2" y="16" width="6" height="6" rx="1"/>
      <rect x="9" y="2" width="6" height="6" rx="1"/>
      <path d="M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3"/>
      <path d="M12 12V8"/>
    </svg>
  `
};

const SearchIcon = {
  template: `
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <circle cx="11" cy="11" r="8"/>
      <path d="m21 21-4.35-4.35"/>
    </svg>
  `
};

const FilterIcon = {
  template: `
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <polygon points="22,3 2,3 10,12.46 10,19 14,21 14,12.46"/>
    </svg>
  `
};

const DownloadIcon = {
  template: `
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
      <polyline points="7,10 12,15 17,10"/>
      <line x1="12" x2="12" y1="15" y2="3"/>
    </svg>
  `
};

const SidebarCloseIcon = {
  template: `
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <rect width="18" height="18" x="3" y="3" rx="2" ry="2"/>
      <path d="M9 3v18"/>
      <path d="m16 15-3-3 3-3"/>
    </svg>
  `
};

const SidebarOpenIcon = {
  template: `
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <rect width="18" height="18" x="3" y="3" rx="2" ry="2"/>
      <path d="M15 3v18"/>
      <path d="m8 9 3 3-3 3"/>
    </svg>
  `
};

const Maximize2Icon = {
  template: `
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <polyline points="15,3 21,3 21,9"/>
      <polyline points="9,21 3,21 3,15"/>
      <line x1="21" x2="14" y1="3" y2="10"/>
      <line x1="3" x2="10" y1="21" y2="14"/>
    </svg>
  `
};

const CheckCircleIcon = {
  template: `
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
      <polyline points="22,4 12,14.01 9,11.01"/>
    </svg>
  `
};

const Loader2Icon = {
  template: `
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M21 12a9 9 0 1 1-6.219-8.56"/>
    </svg>
  `
};

interface Props {
  trace: TraceData;
  analysisSession?: any;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  back: [];
}>();

// Reactive state
const viewMode = ref<'tree' | 'graph'>('tree');
const searchQuery = ref('');
const showDetails = ref(true);
const isFullscreen = ref(false);
const selectedNode = ref<TraceNode | null>(null);

// Computed properties
const isAnalysisCompleted = computed(() => {
  return props.analysisSession?.steps?.every((step: any) => step.status === 'completed') || false;
});

// Methods
const setSelectedNode = (node: TraceNode | null) => {
  selectedNode.value = node;
};
</script>