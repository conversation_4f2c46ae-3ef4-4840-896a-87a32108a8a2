<template>
  <div class="w-full max-w-4xl mx-auto p-6 space-y-8">
    <div class="text-center space-y-6">
      <div class="space-y-2">
        <h1 class="text-4xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
          AI Agent 链路追踪分析
        </h1>
        <p class="text-muted-foreground text-lg max-w-2xl mx-auto">
          输入 Trace ID 来分析 AI Agent 的调用链路和性能指标，获得深入的性能洞察和优化建议
        </p>
      </div>
      
      <div class="flex items-center justify-center gap-8 text-sm text-muted-foreground">
        <div class="flex items-center gap-2">
          <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
          <span>链路信息获取</span>
        </div>
        <div class="flex items-center gap-2">
          <div class="w-2 h-2 bg-amber-500 rounded-full"></div>
          <span>BadCase分析</span>
        </div>
        <div class="flex items-center gap-2">
          <div class="w-2 h-2 bg-green-500 rounded-full"></div>
          <span>AI调优建议</span>
        </div>
      </div>
    </div>

    <div class="p-8 bg-gradient-to-br from-card to-card/50 border-2 rounded-lg border-border">
      <form @submit.prevent="handleSubmit" class="space-y-6">
        <div class="space-y-2">
          <label class="text-sm font-medium">Trace ID</label>
          <div class="flex gap-3">
            <input
              v-model="traceId"
              placeholder="输入 Trace ID (例如: trace_12345678901234567890)"
              class="flex-1 h-12 px-3 py-2 bg-background border border-input rounded-md text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              :disabled="isLoading"
            />
            <button 
              type="submit" 
              :disabled="!traceId.trim() || isLoading"
              class="min-w-32 h-12 px-4 py-2 bg-gradient-to-r from-primary to-primary/80 text-primary-foreground hover:from-primary/90 hover:to-primary/70 inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
            >
              <div v-if="isLoading" class="h-5 w-5 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
              <template v-else>
                <SearchIcon class="h-5 w-5 mr-2" />
                开始分析
              </template>
            </button>
          </div>
        </div>
      </form>

      <div class="mt-8">
        <h3 class="text-sm font-medium text-muted-foreground mb-4">
          或选择示例 Trace ID 快速体验:
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          <button
            v-for="(id, index) in SAMPLE_TRACE_IDS"
            :key="id"
            @click="handleSampleTrace(id)"
            class="justify-start text-xs font-mono p-3 h-auto hover:bg-primary/5 border border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex items-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
            :disabled="isLoading"
          >
            <div class="text-left">
              <div class="font-medium">示例 {{ index + 1 }}</div>
              <div class="text-muted-foreground truncate">
                {{ id.slice(0, 24) }}...
              </div>
            </div>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { SAMPLE_TRACE_IDS } from '@/data/mockTraceData';

// Search icon component (simple SVG)
const SearchIcon = {
  template: `
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <circle cx="11" cy="11" r="8"/>
      <path M="21 21L16.5 16.5"/>
    </svg>
  `
};

interface Props {
  isLoading: boolean;
}

interface Emits {
  traceLoad: [traceId: string];
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const traceId = ref('');

const handleSubmit = () => {
  if (traceId.value.trim()) {
    emit('traceLoad', traceId.value.trim());
  }
};

const handleSampleTrace = (sampleId: string) => {
  traceId.value = sampleId;
  emit('traceLoad', sampleId);
};
</script>