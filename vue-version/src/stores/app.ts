import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { TraceData, TraceNode } from '@/types/trace';
import { AnalysisSession, AnalysisResults } from '@/types/analysis';
import { generateMockTraceData } from '@/data/mockTraceData';

export type ViewType = 'input' | 'analysis' | 'trace-viewer' | 'bad-case-analysis' | 'ai-optimization';

export const useAppStore = defineStore('app', () => {
  // UI State
  const currentView = ref<ViewType>('input');
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const sidebarCollapsed = ref(false);
  const isDarkMode = ref(false);

  // Data State
  const currentTraceData = ref<TraceData | null>(null);
  const analysisSession = ref<AnalysisSession | null>(null);
  const analysisResults = ref<AnalysisResults | null>(null);
  const selectedNode = ref<TraceNode | null>(null);
  const expandedNodes = ref<Set<string>>(new Set());
  const filteredNodeTypes = ref<Set<string>>(new Set(['llm', 'agent', 'tool', 'span', 'generation']));
  const searchQuery = ref('');
  const timeRange = ref<[number, number] | null>(null);

  // Getters
  const filteredTraceNodes = computed(() => {
    if (!currentTraceData.value) return [];
    
    const filterNodes = (node: TraceNode): TraceNode | null => {
      if (!filteredNodeTypes.value.has(node.nodeType)) return null;
      
      if (searchQuery.value && !node.name.toLowerCase().includes(searchQuery.value.toLowerCase())) {
        return null;
      }
      
      const filteredChildren = node.children
        .map(child => filterNodes(child))
        .filter(child => child !== null) as TraceNode[];
      
      return {
        ...node,
        children: filteredChildren
      };
    };
    
    const filtered = filterNodes(currentTraceData.value.rootNode);
    return filtered ? [filtered] : [];
  });

  const currentAnalysisStep = computed(() => {
    if (!analysisSession.value) return null;
    return analysisSession.value.steps[analysisSession.value.currentStep] || null;
  });

  const analysisProgress = computed(() => {
    if (!analysisSession.value) return 0;
    const completedSteps = analysisSession.value.steps.filter(step => step.status === 'completed').length;
    return Math.round((completedSteps / analysisSession.value.steps.length) * 100);
  });

  const hasErrors = computed(() => {
    if (!currentTraceData.value) return false;
    
    const checkForErrors = (node: TraceNode): boolean => {
      if (node.status === 'error') return true;
      return node.children.some(checkForErrors);
    };
    
    return checkForErrors(currentTraceData.value.rootNode);
  });

  const totalTokensUsed = computed(() => {
    return currentTraceData.value?.totalTokens || 0;
  });

  const totalCost = computed(() => {
    return currentTraceData.value?.totalCost || 0;
  });

  // Actions
  const setCurrentView = (view: ViewType) => {
    currentView.value = view;
  };

  const setError = (errorMessage: string | null) => {
    error.value = errorMessage;
  };

  const clearError = () => {
    error.value = null;
  };

  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value;
  };

  const toggleDarkMode = () => {
    isDarkMode.value = !isDarkMode.value;
    // Apply dark mode to document
    if (isDarkMode.value) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };

  const setSelectedNode = (node: TraceNode | null) => {
    selectedNode.value = node;
  };

  const toggleNodeExpansion = (nodeId: string) => {
    const newExpanded = new Set(expandedNodes.value);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    expandedNodes.value = newExpanded;
  };

  const setFilteredNodeTypes = (types: Set<string>) => {
    filteredNodeTypes.value = types;
  };

  const setSearchQuery = (query: string) => {
    searchQuery.value = query;
  };

  const setTimeRange = (range: [number, number] | null) => {
    timeRange.value = range;
  };

  const startTraceAnalysis = async (traceId: string) => {
    try {
      isLoading.value = true;
      error.value = null;
      
      // Simulate API call to fetch trace data
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // For demo purposes, use mock data
      currentTraceData.value = generateMockTraceData(traceId);
      
      // Initialize analysis session
      analysisSession.value = {
        traceId,
        startedAt: new Date().toISOString(),
        currentStep: 0,
        steps: [
          {
            id: 'fetch-trace',
            name: '获取Trace数据',
            description: '从存储中获取完整的trace数据',
            status: 'completed'
          },
          {
            id: 'parse-structure',
            name: '解析结构',
            description: '分析trace的层级结构和节点关系',
            status: 'running'
          },
          {
            id: 'analyze-performance',
            name: '性能分析',
            description: '识别性能瓶颈和优化机会',
            status: 'pending'
          },
          {
            id: 'detect-issues',
            name: '问题检测',
            description: '检测错误、异常和潜在问题',
            status: 'pending'
          },
          {
            id: 'generate-insights',
            name: '生成洞察',
            description: '基于分析结果生成优化建议',
            status: 'pending'
          }
        ]
      };
      
      setCurrentView('analysis');
      
      // Simulate analysis steps
      await simulateAnalysisSteps();
      
    } catch (err) {
      error.value = err instanceof Error ? err.message : '分析失败';
    } finally {
      isLoading.value = false;
    }
  };

  const simulateAnalysisSteps = async () => {
    if (!analysisSession.value) return;
    
    for (let i = 1; i < analysisSession.value.steps.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      analysisSession.value.steps[i - 1].status = 'completed';
      analysisSession.value.steps[i - 1].completedAt = new Date().toISOString();
      
      if (i < analysisSession.value.steps.length) {
        analysisSession.value.steps[i].status = 'running';
        analysisSession.value.steps[i].startedAt = new Date().toISOString();
        analysisSession.value.currentStep = i;
      }
    }
    
    // Complete last step
    const lastStep = analysisSession.value.steps[analysisSession.value.steps.length - 1];
    lastStep.status = 'completed';
    lastStep.completedAt = new Date().toISOString();
    analysisSession.value.completedAt = new Date().toISOString();
    
    // Generate mock analysis results
    generateAnalysisResults();
    
    setCurrentView('trace-viewer');
  };

  const generateAnalysisResults = () => {
    if (!currentTraceData.value) return;
    
    analysisResults.value = {
      traceInfo: {
        summary: {
          totalNodes: 10,
          totalDuration: currentTraceData.value.duration,
          successRate: 0.9,
          errorNodes: ['node-error-1']
        },
        performance: {
          bottlenecks: [
            {
              nodeId: 'llm-analysis',
              nodeName: 'LLM Data Analysis',
              issue: '响应时间过长',
              impact: 'high',
              duration: 5000,
              percentage: 45
            }
          ],
          recommendations: [
            '考虑使用更快的模型',
            '优化prompt长度',
            '增加缓存机制'
          ]
        },
        costs: {
          totalCost: currentTraceData.value.totalCost,
          tokenUsage: {
            prompt: 1500,
            completion: 800,
            total: currentTraceData.value.totalTokens
          },
          breakdown: [
            {
              nodeId: 'llm-planning',
              nodeName: 'LLM Query Understanding',
              model: 'gpt-4',
              inputTokens: 500,
              outputTokens: 200,
              cost: 0.014,
              percentage: 35
            }
          ]
        }
      }
    };
  };

  const resetAnalysis = () => {
    currentTraceData.value = null;
    analysisSession.value = null;
    analysisResults.value = null;
    selectedNode.value = null;
    expandedNodes.value = new Set();
    searchQuery.value = '';
    timeRange.value = null;
    setCurrentView('input');
  };

  return {
    // State
    currentView,
    isLoading,
    error,
    sidebarCollapsed,
    isDarkMode,
    currentTraceData,
    analysisSession,
    analysisResults,
    selectedNode,
    expandedNodes,
    filteredNodeTypes,
    searchQuery,
    timeRange,
    
    // Getters
    filteredTraceNodes,
    currentAnalysisStep,
    analysisProgress,
    hasErrors,
    totalTokensUsed,
    totalCost,
    
    // Actions
    setCurrentView,
    setError,
    clearError,
    toggleSidebar,
    toggleDarkMode,
    setSelectedNode,
    toggleNodeExpansion,
    setFilteredNodeTypes,
    setSearchQuery,
    setTimeRange,
    startTraceAnalysis,
    resetAnalysis
  };
});