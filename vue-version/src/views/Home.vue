<template>
  <div class="min-h-screen bg-background">
    <!-- 顶部导航栏 -->
    <header class="border-b bg-card/50 backdrop-blur supports-[backdrop-filter]:bg-card/50">
      <div class="flex h-16 items-center justify-between px-6">
        <div class="flex items-center gap-3">
          <div class="flex items-center gap-2">
            <ActivityIcon class="h-6 w-6 text-primary" />
            <h1 class="text-xl font-semibold">AI Agent 链路追踪</h1>
          </div>
        </div>
        
        <div class="flex items-center gap-2">
          <button class="px-3 py-1.5 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground rounded-md">
            <SettingsIcon class="h-4 w-4" />
          </button>
          <button class="px-3 py-1.5 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground rounded-md">
            <GithubIcon class="h-4 w-4" />
          </button>
        </div>
      </div>
    </header>

    <!-- 主内容区域 -->
    <main class="flex-1">
      <!-- 输入页面 -->
      <div v-if="currentView === 'input'" class="h-[calc(100vh-4rem)] flex items-center justify-center p-6">
        <div class="w-full max-w-2xl">
          <TraceInput @trace-load="handleTraceLoad" :is-loading="isLoading" />
        </div>
      </div>
      
      <!-- 分析进度页面 -->
      <AnalysisProgress v-else-if="currentView === 'analysis'" />
      
      <!-- 链路追踪详情页面 -->
      <TraceViewer 
        v-else-if="currentView === 'trace-info' && trace" 
        :trace="trace" 
        :analysis-session="analysisSession"
      />
      
      <!-- BadCase分析页面 -->
      <BadCaseAnalysisView 
        v-else-if="currentView === 'badcase-analysis' && trace" 
        :trace="trace" 
        :analysis-session="analysisSession"
      />
      
      <!-- AI优化建议页面 -->
      <AIOptimizationView 
        v-else-if="currentView === 'ai-optimization' && trace" 
        :trace="trace" 
        :analysis-session="analysisSession"
      />
    </main>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useAppStore } from '@/stores/app';
import TraceInput from '@/components/TraceInput.vue';
import AnalysisProgress from '@/components/AnalysisProgress.vue';
// These components will be created later
// import TraceViewer from '@/components/TraceViewer.vue';
// import BadCaseAnalysisView from '@/components/BadCaseAnalysisView.vue';
// import AIOptimizationView from '@/components/AIOptimizationView.vue';

// Temporary placeholder components
const TraceViewer = {
  props: ['trace', 'analysisSession'],
  template: '<div class="p-6"><h2>TraceViewer Component - Coming Soon</h2></div>'
};

const BadCaseAnalysisView = {
  props: ['trace', 'analysisSession'],
  template: '<div class="p-6"><h2>BadCaseAnalysisView Component - Coming Soon</h2></div>'
};

const AIOptimizationView = {
  props: ['trace', 'analysisSession'],
  template: '<div class="p-6"><h2>AIOptimizationView Component - Coming Soon</h2></div>'
};

// Icon components
const ActivityIcon = {
  template: `
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M22 12h-4l-3 9L9 3l-3 9H2"/>
    </svg>
  `
};

const SettingsIcon = {
  template: `
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/>
      <circle cx="12" cy="12" r="3"/>
    </svg>
  `
};

const GithubIcon = {
  template: `
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"/>
      <path d="M9 18c-4.51 2-5-2-7-2"/>
    </svg>
  `
};

const appStore = useAppStore();

const currentView = computed(() => appStore.currentView);
const isLoading = computed(() => appStore.isLoading);
const trace = computed(() => appStore.currentTraceData);
const analysisSession = computed(() => appStore.analysisSession);

const handleTraceLoad = async (traceId: string) => {
  try {
    await appStore.startTraceAnalysis(traceId);
    // Toast notification would go here - for now we'll skip it
    console.log(`开始分析 Trace: ${traceId}`);
  } catch (error) {
    console.error('分析启动失败:', error);
    // Toast notification would go here - for now we'll skip it
  }
};
</script>