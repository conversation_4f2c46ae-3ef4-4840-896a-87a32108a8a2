import { createRouter, createWebHistory } from 'vue-router';
import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: {
      title: 'Echo Lang Pulse - AI Trace Analytics'
    }
  },
  {
    path: '/trace/:id',
    name: 'TraceDetail',
    component: () => import('@/views/Home.vue'),
    props: true,
    meta: {
      title: 'Trace Detail - Echo Lang Pulse'
    }
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  }
});

router.beforeEach((to, from, next) => {
  // Set page title
  if (to.meta?.title) {
    document.title = to.meta.title as string;
  }
  next();
});

export default router;