export interface AnalysisStep {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  startedAt?: string;
  completedAt?: string;
  result?: any;
  error?: string;
}

export interface AnalysisSession {
  traceId: string;
  startedAt: string;
  completedAt?: string;
  currentStep: number;
  steps: AnalysisStep[];
  results?: AnalysisResults;
}

export interface AnalysisResults {
  traceInfo?: TraceAnalysisResult;
  badCaseAnalysis?: BadCaseAnalysisResult;
  aiOptimization?: AIOptimizationResult;
}

export interface TraceAnalysisResult {
  summary: {
    totalNodes: number;
    totalDuration: number;
    successRate: number;
    errorNodes: string[];
  };
  performance: {
    bottlenecks: BottleneckInfo[];
    recommendations: string[];
  };
  costs: {
    totalCost: number;
    tokenUsage: {
      prompt: number;
      completion: number;
      total: number;
    };
    breakdown: CostBreakdown[];
  };
}

export interface BadCaseAnalysisResult {
  issues: BadCaseIssue[];
  severity: 'low' | 'medium' | 'high' | 'critical';
  recommendations: Recommendation[];
  affectedNodes: string[];
}

export interface AIOptimizationResult {
  suggestions: OptimizationSuggestion[];
  expectedImprovements: {
    performanceGain: number;
    costReduction: number;
    reliability: number;
  };
  implementation: {
    priority: 'high' | 'medium' | 'low';
    effort: 'easy' | 'moderate' | 'complex';
    steps: string[];
  };
}

export interface BottleneckInfo {
  nodeId: string;
  nodeName: string;
  issue: string;
  impact: 'high' | 'medium' | 'low';
  duration: number;
  percentage: number;
}

export interface CostBreakdown {
  nodeId: string;
  nodeName: string;
  model?: string;
  inputTokens: number;
  outputTokens: number;
  cost: number;
  percentage: number;
}

export interface BadCaseIssue {
  id: string;
  type: 'error' | 'timeout' | 'performance' | 'cost' | 'quality';
  severity: 'low' | 'medium' | 'high' | 'critical';
  nodeId: string;
  nodeName: string;
  description: string;
  impact: string;
  suggestedFix: string;
}

export interface Recommendation {
  id: string;
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  effort: 'easy' | 'moderate' | 'complex';
  expectedImpact: string;
  steps: string[];
}

export interface OptimizationSuggestion {
  id: string;
  category: 'prompt' | 'model' | 'architecture' | 'parameters' | 'caching';
  title: string;
  description: string;
  currentValue?: string;
  suggestedValue: string;
  reasoning: string;
  expectedImpact: {
    performance?: number;
    cost?: number;
    quality?: number;
  };
  implementation: {
    difficulty: 'easy' | 'moderate' | 'complex';
    steps: string[];
    codeExample?: string;
  };
}