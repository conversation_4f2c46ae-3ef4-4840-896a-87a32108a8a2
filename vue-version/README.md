# AI Agent 链路追踪分析 - Vue 版本

这是将原 React 项目转换为 Vue 3 + TypeScript 的版本，保持了与原项目完全一致的样式和功能。

## 技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **TypeScript** - 类型安全的 JavaScript 超集
- **Vite** - 快速的前端构建工具
- **Pinia** - Vue 状态管理库
- **Vue Router** - Vue 官方路由管理器
- **TanStack Query (Vue)** - 数据获取和状态管理
- **Tailwind CSS** - 实用优先的 CSS 框架

## 项目结构

```
vue-version/
├── src/
│   ├── assets/
│   │   └── css/
│   │       └── index.css          # 全局样式，包含 Tailwind 和设计系统变量
│   ├── components/
│   │   ├── TraceInput.vue          # 链路追踪输入组件
│   │   └── AnalysisProgress.vue    # 分析进度组件
│   ├── data/
│   │   └── mockTraceData.ts        # 模拟数据
│   ├── router/
│   │   └── index.ts                # 路由配置
│   ├── stores/
│   │   └── app.ts                  # Pinia 状态管理
│   ├── types/
│   │   ├── trace.ts                # 链路追踪相关类型
│   │   └── analysis.ts             # 分析相关类型
│   ├── views/
│   │   └── Home.vue                # 主页面组件
│   ├── App.vue                     # 根组件
│   └── main.ts                     # 应用入口
├── index.html                      # HTML 模板
├── package.json                    # 项目依赖和脚本
├── vite.config.ts                  # Vite 配置
├── tsconfig.json                   # TypeScript 配置
├── tailwind.config.js              # Tailwind CSS 配置
└── postcss.config.js               # PostCSS 配置
```

## 安装和运行

### 1. 安装依赖

```bash
cd vue-version
npm install
```

### 2. 启动开发服务器

```bash
npm run dev
```

项目将在 `http://localhost:5173` 启动

### 3. 构建生产版本

```bash
npm run build
```

### 4. 预览生产构建

```bash
npm run preview
```

## 主要功能

- **链路追踪输入** - 支持手动输入 Trace ID 或选择示例数据
- **分析进度展示** - 实时显示分析步骤和完成状态
- **深色/浅色主题** - 支持主题切换，与系统主题同步
- **响应式设计** - 适配不同屏幕尺寸
- **状态管理** - 使用 Pinia 管理应用状态
- **类型安全** - 全面的 TypeScript 类型定义

## 从 React 到 Vue 的主要变更

### 状态管理
- **React**: Zustand
- **Vue**: Pinia

### 数据获取
- **React**: @tanstack/react-query
- **Vue**: @tanstack/vue-query

### 组件系统
- **React**: JSX 函数组件 + Hooks
- **Vue**: 单文件组件 (SFC) + Composition API

### 样式系统
- **保持一致**: 完全相同的 Tailwind CSS 配置和样式变量
- **主题系统**: 保持相同的 CSS 变量和暗/亮模式切换

## 开发说明

本项目完全复刻了原 React 项目的功能和样式：

1. **样式一致性** - 使用相同的 Tailwind 配置和 CSS 变量
2. **功能对等** - 所有核心功能都已转换为 Vue 实现
3. **类型安全** - 保持相同的 TypeScript 类型定义
4. **组件结构** - 逻辑上对应的组件划分

## 待完成组件

以下组件在当前版本中使用了占位符，需要进一步开发：
- `TraceViewer` - 链路追踪详情查看器
- `BadCaseAnalysisView` - BadCase 分析视图
- `AIOptimizationView` - AI 优化建议视图

## 许可证

与原项目保持一致的许可证。